/* General styles */
body {
    font-family: sans-serif;
    line-height: 1.5;
    color: #333;
}

/* Core page container for PDF */
#resume-preview-content {
    width: 210mm;
    min-height: 297mm;           /* allow growth beyond one page */
    padding: 2cm;
    box-sizing: border-box;
    overflow: visible;            /* ensure flowing content isn't clipped */
}

/* Ensure proper A4 pagination */
@page {
    size: A4;
    margin: 0;
}

@media print {
    #resume-preview-content {
        height: auto;            /* multi-page flow */
        min-height: 297mm;
    }
}

/* Avoid breaking important blocks across pages */
.section-margin,
header,
.meta-line,
.draggable {
    break-inside: avoid;
    page-break-inside: avoid;
}

/* Improve text flow near page boundaries */
p, ul, li {
    orphans: 2;
    widows: 2;
}

/* Hide interactive elements (defensive) */
[data-action],
.cursor-grab,
.inline-popover,
#inline-toolbar {
    display: none !important;
}