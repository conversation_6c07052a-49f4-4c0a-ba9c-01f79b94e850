import type { APIRoute } from 'astro';
import * as textract from 'textract';
import Groq from 'groq-sdk';

const groq = new Groq({
    apiKey: import.meta.env.GROQ_API_KEY ?? ''
});

const SUPPORTED_MIME_TYPES = [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'text/plain',
    'text/rtf',
    'application/rtf'
];

// Async text extraction function
async function extractTextFromBuffer(fileBuffer: Buffer, fileType: string): Promise<string> {
    return new Promise((resolve, reject) => {
        textract.fromBufferWithMime(fileType, fileBuffer, (error, text) => {
            if (error) {
                reject(new Error(`Text extraction failed: ${error.message}`));
            } else {
                resolve(text || '');
            }
        });
    });
}

async function cleanResumeText(rawText: string): Promise<string> {
    if (!rawText) {
        return rawText;
    }

    try {
        if (rawText.trim().length === 0) {
            return rawText;
        }

        if (!groq) {
            return rawText;
        }

        let response;
        try {
            response = await groq.chat.completions.create({
                model: 'llama-3.1-8b-instant',
                messages: [
                    {
                        role: "system", 
                        content: `You are a professional resume text cleaner. 
- Preserve ALL original content
- Remove extra whitespaces
- Ensure consistent formatting
- Correct minor typographical errors
- Maintain original document structure
- Return ONLY the cleaned text
- Do NOT add or remove any information`
                    },
                    {
                        role: "user", 
                        content: rawText
                    }
                ],
                max_tokens: 4096,
                temperature: 0.1
            });
        } catch (aiCallError) {
            return rawText;
        }

        const content = response.choices?.[0]?.message?.content;
        return content && typeof content === 'string' 
            ? content.trim() 
            : rawText;

    } catch (error) {
        return rawText;
    }
}

/**
 * Parse resume content and extract structured data for the resume builder
 */
interface ContactInfo {
  name: string;
  email: string;
  phone: string;
  location: string;
  linkedin: string;
  portfolio: string;
}

interface WorkExperience {
  title: string;
  company: string;
  location: string;
  startDate: string;
  endDate: string;
  description: string[];
}

interface Education {
  degree: string;
  school: string;
  location: string;
  startDate: string;
  endDate: string;
  description: string[];
}

interface ParsedResumeData {
  contactInfo: ContactInfo;
  summary: string;
  workExperience: WorkExperience[];
  education: Education[];
  skills: string[] | { category: string; items: string[] }[];
}

/**
 * Use AI to parse skills section into proper categorized format
 */
async function parseSkillsWithAI(skillsText: string): Promise<string[] | { category: string; items: string[] }[]> {
  if (!skillsText.trim() || !groq) {
    return [];
  }

  try {
    const response = await groq.chat.completions.create({
      model: 'llama-3.1-8b-instant',
      messages: [
        {
          role: "system",
          content: `You are a JSON parser for resume skills. You MUST return ONLY valid JSON in one of these two formats:

FORMAT 1 - For categorized skills:
[{"category": "Technical Skills", "items": ["JavaScript", "React", "Node.js"]}, {"category": "Soft Skills", "items": ["Communication", "Leadership"]}]

FORMAT 2 - For simple skill list:
["JavaScript", "React", "Node.js", "Communication", "Leadership"]

CRITICAL RULES:
1. Return ONLY the JSON array, no other text
2. No explanations, no markdown, no code blocks
3. Clean skill names (remove bullets, extra spaces, dashes)
4. If you see "Category: skill1, skill2" format, use FORMAT 1
5. If skills are just listed without categories, use FORMAT 2
6. Split skills on commas, semicolons, or line breaks
7. Remove empty items

EXAMPLES:
Input: "Technical Skills: JavaScript, React\nSoft Skills: Communication"
Output: [{"category": "Technical Skills", "items": ["JavaScript", "React"]}, {"category": "Soft Skills", "items": ["Communication"]}]

Input: "JavaScript, React, Communication, Leadership"
Output: ["JavaScript", "React", "Communication", "Leadership"]`
        },
        {
          role: "user",
          content: skillsText
        }
      ],
      max_tokens: 1000,
      temperature: 0.0
    });

    const content = response.choices?.[0]?.message?.content?.trim();
    if (content) {
      try {
        // Remove any potential markdown code blocks
        const cleanContent = content.replace(/```json\n?/g, '').replace(/```\n?/g, '').trim();
        const parsed = JSON.parse(cleanContent);
        
        // Validate the structure
        if (Array.isArray(parsed)) {
          // Check if it's categorized format
          if (parsed.length > 0 && typeof parsed[0] === 'object' && parsed[0].category && parsed[0].items) {
            // Validate all items have correct structure
            const isValid = parsed.every(item => 
              typeof item === 'object' && 
              typeof item.category === 'string' && 
              Array.isArray(item.items) &&
              item.items.every(skill => typeof skill === 'string')
            );
            if (isValid) return parsed;
          } 
          // Check if it's flat array format
          else if (parsed.every(item => typeof item === 'string')) {
            return parsed;
          }
        }
      } catch (parseError) {
        console.warn('Failed to parse AI response as JSON:', content);
      }
    }
  } catch (error) {
    console.warn('AI skills parsing failed:', error);
  }

  // Enhanced fallback parsing
  const lines = skillsText.split('\n').map(line => line.trim()).filter(line => line);
  const skills: { category: string; items: string[] }[] = [];
  let flatSkills: string[] = [];

  for (const line of lines) {
    // Check for category format (contains colon)
    if (line.includes(':')) {
      const colonIndex = line.indexOf(':');
      const category = line.substring(0, colonIndex).trim();
      const itemsStr = line.substring(colonIndex + 1).trim();
      
      if (category && itemsStr) {
        const items = itemsStr.split(/[,;•\-|]/).map(skill => skill.trim()).filter(skill => skill);
        if (items.length > 0) {
          skills.push({ category, items });
        }
      }
    } else {
      // Parse as flat skills
      const lineSkills = line.split(/[,;•\-|]/).map(skill => skill.trim()).filter(skill => skill);
      flatSkills.push(...lineSkills);
    }
  }

  // Return categorized format if we found categories
  if (skills.length > 0) {
    if (flatSkills.length > 0) {
      skills.push({ category: 'Other Skills', items: flatSkills });
    }
    return skills;
  }

  // Return flat array if no categories found
  return flatSkills;
}

/**
 * Parse resume content and extract structured data for the resume builder
 */
async function parseResumeContent(content: string): Promise<ParsedResumeData> {
  // Basic parsing logic - you can enhance this with more sophisticated parsing
  const lines = content.split('\n').map(line => line.trim()).filter(line => line);
  
  const parsedData: ParsedResumeData = {
    contactInfo: {
      name: '',
      email: '',
      phone: '',
      location: '',
      linkedin: '',
      portfolio: ''
    },
    summary: '',
    workExperience: [],
    education: [],
    skills: []
  };

  let currentSection = '';
  let currentExperience: WorkExperience | null = null;
  let currentEducation: Education | null = null;
  let skillsText = '';

  for (const line of lines) {
    const lowerLine = line.toLowerCase();
    
    // Detect sections
    if (lowerLine.includes('experience') || lowerLine.includes('work')) {
      currentSection = 'experience';
      continue;
    } else if (lowerLine.includes('education')) {
      currentSection = 'education';
      continue;
    } else if (lowerLine.includes('skills')) {
      currentSection = 'skills';
      continue;
    } else if (lowerLine.includes('summary') || lowerLine.includes('objective')) {
      currentSection = 'summary';
      continue;
    }

    // Parse contact info (usually at the top)
    if (!currentSection && !parsedData.contactInfo.name) {
      // First non-empty line is usually the name
      if (!parsedData.contactInfo.name && line.length > 0) {
        parsedData.contactInfo.name = line;
        continue;
      }
      
      // Look for email
      if (line.includes('@') && !parsedData.contactInfo.email) {
        parsedData.contactInfo.email = line;
        continue;
      }
      
      // Look for phone
      if (/\d{3}[-.]?\d{3}[-.]?\d{4}/.test(line) && !parsedData.contactInfo.phone) {
        parsedData.contactInfo.phone = line;
        continue;
      }
      
      // Look for LinkedIn
      if (line.includes('linkedin.com') && !parsedData.contactInfo.linkedin) {
        parsedData.contactInfo.linkedin = line;
        continue;
      }
      
      // Look for portfolio/website
      if ((line.includes('http') || line.includes('www')) && !parsedData.contactInfo.portfolio) {
        parsedData.contactInfo.portfolio = line;
        continue;
      }
    }

    // Parse summary
    if (currentSection === 'summary') {
      if (line.length > 0) {
        parsedData.summary += (parsedData.summary ? ' ' : '') + line;
      }
    }

    // Parse work experience
    if (currentSection === 'experience') {
      // Look for job titles (usually in caps or followed by company)
      if (line.includes(' at ') || line.includes(' - ') || line.includes(' | ')) {
        if (currentExperience) {
          parsedData.workExperience.push(currentExperience);
        }
        
        const parts = line.split(/ at | - | \| /);
        if (parts.length >= 2) {
          currentExperience = {
            title: parts[0].trim(),
            company: parts[1].trim(),
            location: '',
            startDate: '',
            endDate: '',
            description: []
          };
        }
      } else if (currentExperience && line.length > 0) {
        // Add as description bullet point
        if (line.startsWith('•') || line.startsWith('-') || line.startsWith('*')) {
          currentExperience.description.push(line.substring(1).trim());
        } else {
          currentExperience.description.push(line);
        }
      }
    }

    // Parse education
    if (currentSection === 'education') {
      if (line.includes('University') || line.includes('College') || line.includes('School')) {
        if (currentEducation) {
          parsedData.education.push(currentEducation);
        }
        
        currentEducation = {
          degree: '',
          school: line,
          location: '',
          startDate: '',
          endDate: '',
          description: []
        };
      } else if (currentEducation && line.length > 0) {
        if (!currentEducation.degree && line.includes('Bachelor') || line.includes('Master') || line.includes('PhD')) {
          currentEducation.degree = line;
        } else {
          currentEducation.description.push(line);
        }
      }
    }

    // Collect skills text for AI parsing
    if (currentSection === 'skills') {
      if (line.length > 0) {
        skillsText += (skillsText ? '\n' : '') + line;
      }
    }
  }

  // Add any remaining experience/education
  if (currentExperience) {
    parsedData.workExperience.push(currentExperience);
  }
  if (currentEducation) {
    parsedData.education.push(currentEducation);
  }

  // Parse skills using AI
  if (skillsText) {
    parsedData.skills = await parseSkillsWithAI(skillsText);
  }

  return parsedData;
}

export const POST: APIRoute = async ({ request }) => {
    // Validate request content type
    if (request.headers.get('Content-Type') !== 'application/json') {
        return new Response(JSON.stringify({ 
            error: 'Invalid content type' 
        }), { 
            status: 400,
            headers: { 'Content-Type': 'application/json' } 
        });
    }

    try {
        const { fileBase64, fileName, fileType } = await request.json();
        
        // Validate input fields
        if (!fileBase64 || !fileName || !fileType) {
            return new Response(JSON.stringify({
                error: 'Missing required fields: fileBase64, fileName, or fileType'
            }), {
                status: 400,
                headers: { 'Content-Type': 'application/json' }
            });
        }

        // Validate file type
        if (!SUPPORTED_MIME_TYPES.includes(fileType)) {
            return new Response(JSON.stringify({
                error: 'Unsupported file type',
                supportedTypes: SUPPORTED_MIME_TYPES
            }), {
                status: 400,
                headers: { 'Content-Type': 'application/json' }
            });
        }

        // Convert base64 to file buffer
        const fileBuffer = Buffer.from(fileBase64, 'base64');

        // Extract text directly from buffer
        const extractedText = await extractTextFromBuffer(fileBuffer, fileType);

        // Validate extracted text
        if (!extractedText || extractedText.trim().length === 0) {
            return new Response(JSON.stringify({ 
                success: false,
                error: 'No text could be extracted from the document',
                details: 'The document appears to be empty or unreadable'
            }), { 
                status: 400,
                headers: { 'Content-Type': 'application/json' } 
            });
        }

        // Clean resume text using Llama 3.1-8b-instant
        const cleanedText = await cleanResumeText(extractedText);

        // Parse the cleaned text into structured data
        const parsedResume = await parseResumeContent(cleanedText);

        return new Response(JSON.stringify({ 
            success: true, 
            data: {
                text: cleanedText,
                parsed: parsedResume
            }
        }), { 
            status: 200,
            headers: { 'Content-Type': 'application/json' } 
        });

    } catch (error) {
        console.error('Resume upload error:', error);
        
        const errorMessage = error instanceof Error 
            ? error.message 
            : 'An unexpected error occurred while processing the resume';

        return new Response(JSON.stringify({ 
            success: false,
            error: 'Failed to upload resume',
            details: errorMessage
        }), { 
            status: 500,
            headers: { 'Content-Type': 'application/json' } 
        });
    }
};