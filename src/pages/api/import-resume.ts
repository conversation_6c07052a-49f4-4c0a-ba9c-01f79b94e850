import type { APIRoute } from 'astro';
import { getAuth } from 'firebase-admin/auth';
import { getFirestore } from 'firebase-admin/firestore';
import { initializeApp, getApps, cert } from 'firebase-admin/app';

// Initialize Firebase Admin if not already initialized
if (getApps().length === 0) {
  initializeApp({
    credential: cert(JSON.parse(process.env.FIREBASE_ADMIN_KEY || '{}')),
  });
}

const auth = getAuth();
const db = getFirestore();

export const POST: APIRoute = async ({ request }) => {
  try {
    // Get the authorization header
    const authHeader = request.headers.get('Authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return new Response(JSON.stringify({ 
        error: 'Unauthorized - No valid token provided' 
      }), { 
        status: 401,
        headers: { 'Content-Type': 'application/json' } 
      });
    }

    const token = authHeader.split('Bearer ')[1];
    
    // Verify the token
    let decodedToken;
    try {
      decodedToken = await auth.verifyIdToken(token);
    } catch (error) {
      return new Response(JSON.stringify({ 
        error: 'Unauthorized - Invalid token' 
      }), { 
        status: 401,
        headers: { 'Content-Type': 'application/json' } 
      });
    }

    const userId = decodedToken.uid;
    const { resumeId } = await request.json();

    if (!resumeId) {
      return new Response(JSON.stringify({ 
        error: 'Missing resumeId parameter' 
      }), { 
        status: 400,
        headers: { 'Content-Type': 'application/json' } 
      });
    }

    // Get the resume from Firestore
    const resumeRef = db.collection('users').doc(userId).collection('resumes').doc(resumeId);
    const resumeDoc = await resumeRef.get();

    if (!resumeDoc.exists) {
      return new Response(JSON.stringify({ 
        error: 'Resume not found' 
      }), { 
        status: 404,
        headers: { 'Content-Type': 'application/json' } 
      });
    }

    const resumeData = resumeDoc.data();
    
    if (!resumeData) {
      return new Response(JSON.stringify({ 
        error: 'Resume data is empty' 
      }), { 
        status: 404,
        headers: { 'Content-Type': 'application/json' } 
      });
    }

    // Parse the resume content and extract structured data
    const parsedResume = await parseResumeContent(resumeData.content || '');

    return new Response(JSON.stringify({
      success: true,
      data: {
        ...parsedResume,
        resumeId: resumeId,
        resumeName: resumeData.name || 'Imported Resume'
      }
    }), { 
      status: 200,
      headers: { 'Content-Type': 'application/json' } 
    });

  } catch (error) {
    console.error('Error importing resume:', error);
    return new Response(JSON.stringify({ 
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }), { 
      status: 500,
      headers: { 'Content-Type': 'application/json' } 
    });
  }
};

interface ContactInfo {
  name: string;
  email: string;
  phone: string;
  location: string;
  linkedin: string;
  portfolio: string;
}

interface WorkExperience {
  title: string;
  company: string;
  location: string;
  startDate: string;
  endDate: string;
  description: string[];
}

interface Education {
  degree: string;
  school: string;
  location: string;
  startDate: string;
  endDate: string;
  description: string[];
}

interface ParsedResumeData {
  contactInfo: ContactInfo;
  summary: string;
  workExperience: WorkExperience[];
  education: Education[];
  skills: string[] | { category: string; items: string[] }[];
}

/**
 * Enhanced skills parsing for import-resume
 */
async function parseSkillsWithAI(skillsText: string): Promise<string[] | { category: string; items: string[] }[]> {
  if (!skillsText.trim()) {
    return [];
  }

  // Enhanced parsing logic for import-resume
  const lines = skillsText.split('\n').map(line => line.trim()).filter(line => line);
  const skills: { category: string; items: string[] }[] = [];
  let flatSkills: string[] = [];

  for (const line of lines) {
    // Check for category format (contains colon)
    if (line.includes(':')) {
      const colonIndex = line.indexOf(':');
      const category = line.substring(0, colonIndex).trim();
      const itemsStr = line.substring(colonIndex + 1).trim();
      
      if (category && itemsStr) {
        const items = itemsStr.split(/[,;•\-|]/).map(skill => skill.trim()).filter(skill => skill);
        if (items.length > 0) {
          skills.push({ category, items });
        }
      }
    } else {
      // Parse as flat skills
      const lineSkills = line.split(/[,;•\-|]/).map(skill => skill.trim()).filter(skill => skill);
      flatSkills.push(...lineSkills);
    }
  }

  // Return categorized format if we found categories
  if (skills.length > 0) {
    if (flatSkills.length > 0) {
      skills.push({ category: 'Other Skills', items: flatSkills });
    }
    return skills;
  }

  // Return flat array if no categories found
  return flatSkills;
}

/**
 * Parse resume content and extract structured data for the resume builder
 */
async function parseResumeContent(content: string): Promise<ParsedResumeData> {
  // Basic parsing logic - you can enhance this with more sophisticated parsing
  const lines = content.split('\n').map(line => line.trim()).filter(line => line);
  
  const parsedData: ParsedResumeData = {
    contactInfo: {
      name: '',
      email: '',
      phone: '',
      location: '',
      linkedin: '',
      portfolio: ''
    },
    summary: '',
    workExperience: [],
    education: [],
    skills: []
  };

  let currentSection = '';
  let currentExperience: WorkExperience | null = null;
  let currentEducation: Education | null = null;
  let skillsText = '';

  for (const line of lines) {
    const lowerLine = line.toLowerCase();
    
    // Detect sections
    if (lowerLine.includes('experience') || lowerLine.includes('work')) {
      currentSection = 'experience';
      continue;
    } else if (lowerLine.includes('education')) {
      currentSection = 'education';
      continue;
    } else if (lowerLine.includes('skills')) {
      currentSection = 'skills';
      continue;
    } else if (lowerLine.includes('summary') || lowerLine.includes('objective')) {
      currentSection = 'summary';
      continue;
    }

    // Parse contact info (usually at the top)
    if (!currentSection && !parsedData.contactInfo.name) {
      // First non-empty line is usually the name
      if (!parsedData.contactInfo.name && line.length > 0) {
        parsedData.contactInfo.name = line;
        continue;
      }
      
      // Look for email
      if (line.includes('@') && !parsedData.contactInfo.email) {
        parsedData.contactInfo.email = line;
        continue;
      }
      
      // Look for phone
      if (/\d{3}[-.]?\d{3}[-.]?\d{4}/.test(line) && !parsedData.contactInfo.phone) {
        parsedData.contactInfo.phone = line;
        continue;
      }
      
      // Look for LinkedIn
      if (line.includes('linkedin.com') && !parsedData.contactInfo.linkedin) {
        parsedData.contactInfo.linkedin = line;
        continue;
      }
      
      // Look for portfolio/website
      if ((line.includes('http') || line.includes('www')) && !parsedData.contactInfo.portfolio) {
        parsedData.contactInfo.portfolio = line;
        continue;
      }
    }

    // Parse summary
    if (currentSection === 'summary') {
      if (line.length > 0) {
        parsedData.summary += (parsedData.summary ? ' ' : '') + line;
      }
    }

    // Parse work experience
    if (currentSection === 'experience') {
      // Look for job titles (usually in caps or followed by company)
      if (line.includes(' at ') || line.includes(' - ') || line.includes(' | ')) {
        if (currentExperience) {
          parsedData.workExperience.push(currentExperience);
        }
        
        const parts = line.split(/ at | - | \| /);
        if (parts.length >= 2) {
          currentExperience = {
            title: parts[0].trim(),
            company: parts[1].trim(),
            location: '',
            startDate: '',
            endDate: '',
            description: []
          };
        }
      } else if (currentExperience && line.length > 0) {
        // Add as description bullet point
        if (line.startsWith('•') || line.startsWith('-') || line.startsWith('*')) {
          currentExperience.description.push(line.substring(1).trim());
        } else {
          currentExperience.description.push(line);
        }
      }
    }

    // Parse education
    if (currentSection === 'education') {
      if (line.includes('University') || line.includes('College') || line.includes('School')) {
        if (currentEducation) {
          parsedData.education.push(currentEducation);
        }
        
        currentEducation = {
          degree: '',
          school: line,
          location: '',
          startDate: '',
          endDate: '',
          description: []
        };
      } else if (currentEducation && line.length > 0) {
        if (!currentEducation.degree && line.includes('Bachelor') || line.includes('Master') || line.includes('PhD')) {
          currentEducation.degree = line;
        } else {
          currentEducation.description.push(line);
        }
      }
    }

    // Collect skills text for AI parsing
    if (currentSection === 'skills') {
      if (line.length > 0) {
        skillsText += (skillsText ? '\n' : '') + line;
      }
    }
  }

  // Add any remaining experience/education
  if (currentExperience) {
    parsedData.workExperience.push(currentExperience);
  }
  if (currentEducation) {
    parsedData.education.push(currentEducation);
  }

  // Parse skills using enhanced parsing
  if (skillsText) {
    parsedData.skills = await parseSkillsWithAI(skillsText);
  }

  return parsedData;
}
