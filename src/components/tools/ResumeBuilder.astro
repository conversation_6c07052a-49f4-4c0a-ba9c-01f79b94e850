---
import ResumeForm from "../resume-builder/ResumeForm.astro";
import ResumePreview from "../resume-builder/ResumePreview.astro";
import ResumeCustomizer from "../resume-builder/ResumeCustomizer.astro";
import SectionNav from "../resume-builder/SectionNav.astro";
import SectionEditor from "../resume-builder/SectionEditor.astro";
---

<div class="relative w-full max-w-[1280px] px-6 md:px-8 mx-auto">
  

  <div id="resume-builder-container" class="grid grid-cols-1 gap-6 items-start justify-center w-fit mx-auto">
    <!-- Left Nav + Editor -->
    <div id="resume-form-container" class="hidden" aria-hidden="true">
      <div class="space-y-4">
        <div class="bg-white/80 dark:bg-gray-800/50 backdrop-blur-xl border border-gray-200/50 dark:border-gray-700/30 rounded-2xl shadow-lg p-6">
          <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-4">Quick Start</h3>
          <div class="space-y-3">
            <button id="upload-resume-btn" class="w-full px-4 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors font-medium flex items-center justify-center gap-2">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                <path stroke-linecap="round" stroke-linejoin="round" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 19l-7-5-7 5" />
                  </svg>
              <span>Upload Existing</span>
            </button>
            <input id="resume-file-input" type="file" class="hidden" accept=".pdf,.doc,.docx,.txt,.rtf" />
          </div>
        </div>
        <SectionNav />
        <SectionEditor />
      </div>
    </div>

    <!-- Preview and Customization Section -->
    <div id="resume-preview-wrapper" class="w-auto transition-all duration-500 ease-in-out">
      <div id="resume-preview-flex-container" class="flex gap-4 justify-start">
        <!-- Preview Content -->
        <div id="resume-preview-content-wrapper" class="flex-none transition-all duration-500 ease-in-out">
          <ResumePreview />
        </div>

        <!-- Customization Panel (right) - disabled since controls are moved into Sections palette -->
        <div id="resume-customizer-container" class="customizer-panel hidden transition-all duration-500 ease-in-out" aria-hidden="true">
          <!-- Intentionally hidden -->
        </div>
      </div>
    </div>
  </div>

  <!-- Mobile Backdrop -->
  <div id="mobile-backdrop" class="fixed inset-0 bg-black bg-opacity-50 z-40 hidden transition-opacity duration-300"></div>
</div>

<script>
  import { setResumeData } from '../../lib/resumeBuilderService';

  document.addEventListener('DOMContentLoaded', () => {
    const container = document.getElementById('resume-builder-container');
    const formContainer = document.getElementById('resume-form-container');
    const previewWrapper = document.getElementById('resume-preview-wrapper');
    const previewFlexContainer = document.getElementById('resume-preview-flex-container');
    const customizeBtn = document.getElementById('customize-btn');
    const customizerContainer = document.getElementById('resume-customizer-container');
    const backToEditBtn = document.getElementById('back-to-edit-btn');
    const startFreshBtn = document.getElementById('start-fresh-btn');
    // const importResumeBtn = null; // removed UI
    const uploadResumeBtn = document.getElementById('upload-resume-btn');
    const resumeFileInput = document.getElementById('resume-file-input') as HTMLInputElement;
    const progressBar = null;
    const progressText = null;
    const mobileBackdrop = document.getElementById('mobile-backdrop');

    if (!container || !formContainer || !previewWrapper) return;


    // Check if we're on mobile/tablet
    const isMobile = () => window.innerWidth < 1280;

    // Customization panel toggle (only if toolbar + right panel are present)
    if (customizeBtn && customizerContainer && backToEditBtn) {
      customizeBtn.addEventListener('click', () => {
      // Toggle buttons immediately
      customizeBtn.classList.add('hidden');
      backToEditBtn.classList.remove('hidden');

      // Reserve space behavior for preview content right away to avoid left-shift
      previewFlexContainer?.classList.add('customizer-active');

      // Step 1: Start hiding form and expanding preview simultaneously
      formContainer.classList.add('form-hidden');
      // Collapse grid's left column so empty space disappears in customize view
      container.classList.add('form-collapsed');
      previewWrapper.classList.add('preview-expanded');

      // Step 2: Open customizer immediately (overlay, no layout impact)
        customizerContainer.classList.add('customizer-open');
        // Add root flag so inner components (e.g., Sections palette) can react via CSS
        document.documentElement.classList.add('customizer-open-root');

      // Show mobile backdrop if on mobile
      if (isMobile() && mobileBackdrop) {
        mobileBackdrop.classList.remove('hidden');
        mobileBackdrop.classList.add('opacity-100');
      }
    });
    }

    const closeCustomizer = () => {
      if (!(customizeBtn && customizerContainer && backToEditBtn)) return;
      // Toggle buttons immediately
      customizeBtn.classList.remove('hidden');
      backToEditBtn.classList.add('hidden');

      // Step 1: Close customization panel first
      customizerContainer.classList.remove('customizer-open');
      // Remove root flag so inner components can re-show themselves
      document.documentElement.classList.remove('customizer-open-root');

      // Step 2: Wait a bit, then start showing form and shrinking preview
      setTimeout(() => {
        formContainer.classList.remove('form-hidden');
        // Restore grid columns
        container.classList.remove('form-collapsed');
        previewWrapper.classList.remove('preview-expanded');
        previewFlexContainer?.classList.remove('customizer-active');
      }, 150);

      // Hide mobile backdrop
      if (mobileBackdrop) {
        mobileBackdrop.classList.remove('opacity-100');
        setTimeout(() => mobileBackdrop.classList.add('hidden'), 300);
      }
    };

    if (backToEditBtn) backToEditBtn.addEventListener('click', closeCustomizer);

    // Close customizer when clicking backdrop on mobile
    if (mobileBackdrop && backToEditBtn) {
      mobileBackdrop.addEventListener('click', closeCustomizer);
    }

    // Close customizer when clicking the close button in the panel
    document.addEventListener('click', (e) => {
      if ((e.target as HTMLElement)?.id === 'close-customizer-btn' ||
          (e.target as HTMLElement)?.closest('#close-customizer-btn')) {
        closeCustomizer();
      }
    });

    // Handle window resize
    window.addEventListener('resize', () => {
      if (!isMobile() && mobileBackdrop) {
        mobileBackdrop.classList.remove('opacity-100');
        mobileBackdrop.classList.add('hidden');
      }
    });

    // Start fresh functionality
    if (startFreshBtn) {
      startFreshBtn.addEventListener('click', () => {
        // Clear any uploaded data and focus on first form section
        const firstAccordion = document.querySelector('.accordion-button') as HTMLButtonElement;
        if (firstAccordion && firstAccordion.classList.contains('collapsed')) {
          firstAccordion.click();
        }

        // Scroll to form
        formContainer.scrollIntoView({ behavior: 'smooth', block: 'start' });
      });
    }

    // Import-from-resume functionality removed per request

    // Upload resume file functionality
    if (uploadResumeBtn && resumeFileInput) {
      uploadResumeBtn.addEventListener('click', () => {
        resumeFileInput.click();
      });

      resumeFileInput.addEventListener('change', async (event) => {
        const target = event.target as HTMLInputElement;
        const file = target.files?.[0];

        if (!file) return;

        try {
          // Get current user token
          const { getAuth } = await import('firebase/auth');
          const auth = getAuth();
          const user = auth.currentUser;
          
          if (!user) {
            console.error('User not authenticated');
            return;
          }

          const idToken = await user.getIdToken();
          
          // Convert file to base64
          const base64 = await fileToBase64(file);
          
          // Call the upload API
          const response = await fetch('/.netlify/functions/upload-resume', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${idToken}`
            },
            body: JSON.stringify({
              fileBase64: base64,
              fileName: file.name,
              fileType: file.type
            })
          });

          if (!response.ok) {
            throw new Error('Failed to upload resume');
          }

          const result = await response.json();
          
          if (result.success && result.data.parsed) {
            // Helper to normalize common date strings
            const normalizeDateString = (input) => {
              if (!input) return '';
              const str = String(input).trim();
              if (/^(present|current)$/i.test(str)) return 'Present';
              if (/^\d{4}-\d{2}$/.test(str)) return str;
              const monthMap = {jan:1,january:1,feb:2,february:2,mar:3,march:3,apr:4,april:4,may:5,jun:6,june:6,jul:7,july:7,aug:8,august:8,sep:9,sept:9,september:9,oct:10,october:10,nov:11,november:11,dec:12,december:12};
              const m = str.match(/([A-Za-z]{3,9})\s+(\d{4})/);
              if (m) { const mm = monthMap[m[1].toLowerCase()]; const yyyy = m[2]; if (mm) return `${yyyy}-${String(mm).padStart(2,'0')}`; }
              const y = str.match(/^(\d{4})$/); if (y) return `${y[1]}-01`;
              return '';
            };

            // Transform the API response to match the resume builder data structure
            const transformedData = {
              contactInfo: {
                fullName: result.data.parsed.contactInfo?.name || '',
                email: result.data.parsed.contactInfo?.email || '',
                phone: result.data.parsed.contactInfo?.phone || '',
                location: result.data.parsed.contactInfo?.location || '',
                linkedin: result.data.parsed.contactInfo?.linkedin || '',
                portfolio: result.data.parsed.contactInfo?.portfolio || ''
              },
              summary: result.data.parsed.summary || '',
              workExperience: result.data.parsed.workExperience?.map((exp: any, index: number) => ({
                id: crypto.randomUUID(),
                jobTitle: exp.title || exp.jobTitle || '',
                company: exp.company || '',
                location: exp.location || '',
                startDate: normalizeDateString(exp.startDate || exp.start || ''),
                endDate: normalizeDateString(exp.endDate || exp.end || ''),
                bulletPoints: exp.description?.map((desc: string) => ({
                  id: crypto.randomUUID(),
                  text: desc
                })) || []
              })) || [],
              education: result.data.parsed.education?.map((edu: any, index: number) => ({
                id: crypto.randomUUID(),
                institution: edu.school || edu.institution || '',
                degree: edu.degree || '',
                fieldOfStudy: '',
                graduationDate: normalizeDateString(edu.endDate || edu.graduationDate || ''),
                details: edu.description?.join(' ') || ''
              })) || [],
              skills: result.data.parsed.skills || [],
              customSections: (result.data.parsed.projects && Array.isArray(result.data.parsed.projects) && result.data.parsed.projects.length)
                ? [
                    {
                      id: crypto.randomUUID(),
                      title: 'Projects',
                      content: `<ul>${result.data.parsed.projects.map((p:any) => {
                        const name = p.name || p.title || '';
                        const bullets = Array.isArray(p.description) ? p.description : (p.details ? [p.details] : []);
                        const body = bullets.length ? `<ul>${bullets.map((b:string)=>`<li>${b}</li>`).join('')}</ul>` : '';
                        return `<li><strong>${name}</strong>${body}</li>`;
                      }).join('')}</ul>`,
                      visible: true,
                    }
                  ]
                : []
            };
            
            console.log('Setting resume data:', transformedData);
            console.log('Summary length:', transformedData.summary?.length);
            console.log('Work experience count:', transformedData.workExperience?.length);
            console.log('Education count:', transformedData.education?.length);
            console.log('Skills count:', transformedData.skills?.length);
            setResumeData(transformedData);
            
            // Scroll to form to show the imported data
            formContainer.scrollIntoView({ behavior: 'smooth', block: 'start' });
            
            // Show success message
            console.log('Resume uploaded and parsed successfully:', file.name);
            
            // Clear the file input
            resumeFileInput.value = '';
          }
        } catch (error) {
          console.error('Error uploading resume:', error);
        }
      });
    }

    // Helper function to convert file to base64
    function fileToBase64(file: File): Promise<string> {
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = () => {
          const result = reader.result as string;
          // Remove the data URL prefix (e.g., "data:application/pdf;base64,")
          const base64 = result.split(',')[1];
          resolve(base64);
        };
        reader.onerror = error => reject(error);
      });
    }

    // Initialize progress
    // progress removed
  });
</script>

<style>
  .max-h-screen {
    max-height: 100vh;
  }

  /* Form hiding animation - faster transition */
  #resume-form-container {
    transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1), opacity 0.25s ease;
    transform: translateX(0);
    opacity: 1;
    flex-shrink: 0;
  }

  #resume-form-container.form-hidden {
    transform: translateX(-100%);
    opacity: 0;
    pointer-events: none;
    width: 0 !important;
    min-width: 0 !important;
    margin: 0 !important;
    padding: 0 !important;
    height: 0 !important;           /* prevent the hidden form from contributing row height */
    max-height: 0 !important;
    overflow: hidden !important;
  }

  /* Preview expansion - only animate transform/opacity to avoid layout jump */
  #resume-preview-wrapper {
    transition: transform 0.35s cubic-bezier(0.4, 0, 0.2, 1), opacity 0.25s ease;
  }

  /* When preview is expanded, take full container width */
  #resume-preview-wrapper.preview-expanded {
    width: 100% !important; /* static width to prevent shift */
    flex: 1 !important;
  }

  /* Ensure the flex container also expands */
  #resume-preview-wrapper.preview-expanded #resume-preview-flex-container {
    width: 100% !important;
  }

  /* Make preview wrapper normal flow without scroll constraints */
  #resume-preview-content-wrapper {
    overflow: visible;
    max-height: none;
  }

  /* Customization Panel Styles - transform based to avoid flex squeeze */
  .customizer-panel {
    opacity: 0;
    pointer-events: none;
    overflow: hidden;
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1), opacity 0.2s ease;
  }

  .customizer-panel.customizer-open {
    opacity: 1;
    pointer-events: auto;
  }

  /* Mobile backdrop */
  #mobile-backdrop {
    opacity: 0;
    transition: opacity 0.3s ease-in-out;
  }

  #mobile-backdrop.opacity-100 {
    opacity: 1;
  }

  /* Responsive adjustments */
  @media (max-width: 1279px) {
    #resume-form-container.form-hidden {
      transform: translateY(-100%);
    }

    .customizer-panel {
      position: fixed;
      top: 0;
      right: 0;
      height: 100vh;
      width: 90vw;
      max-width: 420px;
      z-index: 50;
      background: white;
      box-shadow: -4px 0 6px -1px rgb(0 0 0 / 0.1);
      border-left: 1px solid rgb(229 231 235);
      transform: translateX(100%);
    }

    .customizer-panel.customizer-open {
      transform: translateX(0);
      will-change: transform;
    }

    .dark .customizer-panel.customizer-open {
      background: rgb(31 41 55);
      border-left-color: rgb(75 85 99);
    }

    /* Better mobile preview sizing */
    #resume-preview-wrapper.preview-expanded {
      width: 100% !important;
    }

    #resume-preview-wrapper.preview-expanded #resume-preview-content-wrapper {
      max-width: 100%;
      padding-right: 0;
    }
  }

  /* Collapse left column when the form is hidden (customize view) */
  @media (min-width: 1024px) {
    #resume-builder-container.form-collapsed {
      /* keep two tracks but shrink the left one to zero so items stay on the same row */
      grid-template-columns: 0px auto !important;
    }
  }

  @media (min-width: 1280px) {
    /* Desktop: center preview; side panels hug the page */
    #resume-preview-flex-container {
      position: relative;
      justify-content: center;
      align-items: flex-start;
    }

    #resume-preview-wrapper { width: auto; }
    #resume-preview-content-wrapper { flex: 0 0 auto; }

    /* IDENTICAL look/feel to Sections palette (left) */
    .customizer-panel {
      position: sticky;                 /* same behavior as left palette */
      top: 80px;                        /* similar to top-20 */
      align-self: flex-start;
      width: 240px;                     /* mirror left palette width */
      max-width: 240px;
      opacity: 0;
      pointer-events: none;
      transform: translateX(8px);
      transition: opacity 0.2s ease, transform 0.2s ease;
      display: none;                    /* not rendered until open */
    }
    .customizer-panel.customizer-open {
      display: block;
      opacity: 1;
      pointer-events: auto;
      transform: none;
    }

    /* Card visuals: mirror the Sections palette styles */
    .customizer-panel > * {
      background: rgba(255,255,255,0.7);
      backdrop-filter: blur(8px);
      border: 1px solid rgba(229,231,235,1);
      border-radius: 0.75rem;
    }
    .dark .customizer-panel > * {
      background: rgba(31,41,55,0.5);
      border-color: rgb(75 85 99);
    }

    /* No artificial right padding needed when docked */
    #resume-preview-flex-container.customizer-active #resume-preview-content-wrapper {
      padding-right: 0;
      transition: padding-right 0.2s ease;
    }
  }

  /* Form section improvements */
  #resume-form-container {
    max-height: calc(100vh - 7rem);
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: rgb(156 163 175) transparent;
  }

  #resume-form-container::-webkit-scrollbar {
    width: 6px;
  }

  #resume-form-container::-webkit-scrollbar-track {
    background: transparent;
  }

  #resume-form-container::-webkit-scrollbar-thumb {
    background-color: rgb(156 163 175);
    border-radius: 3px;
  }

  /* Progress bar animation */
  #progress-bar {
    transition: width 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* Smooth transitions for layout changes */
  #resume-builder-container {
    transition: opacity 0.2s ease-in-out, transform 0.2s ease-in-out;
  }

  /* Mobile optimizations */
  @media (max-width: 1279px) {
    #resume-builder-container {
      flex-direction: column;
    }

    #resume-form-container,
    #resume-preview-wrapper {
      width: 100%;
    }

    #resume-preview-flex-container {
      position: relative;
      top: auto;
    }
  }
</style>
</style>
</style>