---
import FloatingToolbar from './FloatingToolbar.astro';
---

<div class="bg-transparent focus:outline-none relative">

   <!-- Upload overlay spinner -->
   <div id="upload-overlay" class="hidden fixed inset-0 z-[9999] flex items-center justify-center bg-white/65 dark:bg-gray-900/70 backdrop-blur-sm">
     <div class="flex flex-col items-center gap-3 px-6 py-5 rounded-xl border border-gray-200/70 dark:border-gray-700/60 bg-white/90 dark:bg-gray-800/80 shadow-lg">
       <svg class="h-6 w-6 text-indigo-600 animate-spin" viewBox="0 0 24 24" aria-hidden="true">
         <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4" fill="none"></circle>
         <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z"></path>
       </svg>
       <div class="text-sm font-medium text-gray-700 dark:text-gray-200">Uploading resume…</div>
     </div>
     <span class="sr-only" role="status" aria-live="polite">Uploading resume</span>
   </div>

   <!-- Layout: add a lightweight left Sections palette -->
    <div class="mx-auto w-full max-w-6xl grid grid-cols-1 md:grid-cols-[240px_minmax(0,1fr)] gap-4">
      <!-- Sections palette -->
      <aside id="sections-palette" class="hidden md:block self-start sticky top-20 bg-white/70 dark:bg-gray-800/50 backdrop-blur rounded-xl border border-gray-200/60 dark:border-gray-700/50 p-3">
        <button id="sections-upload-btn" class="w-full mb-3 inline-flex items-center justify-center gap-1.5 text-xs font-semibold px-2.5 py-1.5 rounded-md bg-emerald-50 text-emerald-700 hover:bg-emerald-100 dark:bg-emerald-900/30 dark:text-emerald-300 dark:hover:bg-emerald-800/40">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
            <path stroke-linecap="round" stroke-linejoin="round" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 19l-7-5-7 5" />
          </svg>
          <span>Upload Existing</span>
        </button>
        <h3 class="text-sm font-semibold text-gray-700 dark:text-gray-200 mb-2">Sections</h3>
        <div class="space-y-1" id="sections-default">
          <!-- Sections will be dynamically rendered here based on sectionsOrder -->
        </div>

        <!-- Visible custom sections appear here -->
        <div id="sections-custom-visible" class="space-y-1 mt-2 hidden" aria-live="polite"></div>

        <div class="mt-3">
          <button id="more-sections-toggle" class="w-full inline-flex items-center justify-between text-xs font-semibold px-2 py-1.5 rounded-md bg-gray-100 dark:bg-gray-700/60 text-gray-700 dark:text-gray-200">
            <span>More Section</span>
            <svg class="w-4 h-4 opacity-70" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true"><path fill-rule="evenodd" d="M5.23 7.21a.75.75 0 011.06.02L10 10.94l3.71-3.71a.75.75 0 011.08 1.04l-4.25 4.25a.75.75 0 01-1.08 0L5.21 8.27a.75.75 0 01.02-1.06z" clip-rule="evenodd"/></svg>
          </button>
          <div id="more-sections" class="mt-2 hidden space-y-1">
            <div class="section-row" data-more-row="Projects">
              <button class="section-chip chip-main" data-more="Projects" data-more-item="Projects" aria-label="Scroll to Projects">Projects</button>
              <button class="chip-act" data-action="toggle-custom" data-title="Projects" aria-label="Add Projects">+</button>
            </div>
            <div class="section-row" data-more-row="Certifications">
              <button class="section-chip chip-main" data-more="Certifications" data-more-item="Certifications" aria-label="Scroll to Certifications">Certifications</button>
              <button class="chip-act" data-action="toggle-custom" data-title="Certifications" aria-label="Add Certifications">+</button>
            </div>
            <div class="section-row" data-more-row="Publications">
              <button class="section-chip chip-main" data-more="Publications" data-more-item="Publications" aria-label="Scroll to Publications">Publications</button>
              <button class="chip-act" data-action="toggle-custom" data-title="Publications" aria-label="Add Publications">+</button>
            </div>
            <div class="section-row" data-more-row="Awards">
              <button class="section-chip chip-main" data-more="Awards" data-more-item="Awards" aria-label="Scroll to Awards">Awards</button>
              <button class="chip-act" data-action="toggle-custom" data-title="Awards" aria-label="Add Awards">+</button>
            </div>
            <div class="section-row" data-more-row="Languages">
              <button class="section-chip chip-main" data-more="Languages" data-more-item="Languages" aria-label="Scroll to Languages">Languages</button>
              <button class="chip-act" data-action="toggle-custom" data-title="Languages" aria-label="Add Languages">+</button>
            </div>
            <div class="section-row" data-more-row="Interests">
              <button class="section-chip chip-main" data-more="Interests" data-more-item="Interests" aria-label="Scroll to Interests">Interests</button>
              <button class="chip-act" data-action="toggle-custom" data-title="Interests" aria-label="Add Interests">+</button>
            </div>
            <div class="section-row" data-more-row="Volunteer">
              <button class="section-chip chip-main" data-more="Volunteer" data-more-item="Volunteer" aria-label="Scroll to Volunteer">Volunteer</button>
              <button class="chip-act" data-action="toggle-custom" data-title="Volunteer" aria-label="Add Volunteer">+</button>
            </div>
            <div class="section-row" data-more-row="Custom">
              <button class="section-chip chip-main" data-more="Custom" data-more-item="Custom" aria-label="Scroll to Custom">Custom</button>
              <button class="chip-act" data-action="toggle-custom" data-title="Custom" aria-label="Add Custom">+</button>
            </div>
          </div>
          <p class="mt-3 text-[11px] text-gray-500 dark:text-gray-400">Click the name to scroll. Use + to add, - to remove.</p>
        </div>

        <!-- Inline Customize panel inside Sections sidebar (no external component) -->
        <div class="mt-4">
          <button id="palette-customize-toggle" class="w-full inline-flex items-center justify-between text-xs font-semibold px-2 py-1.5 rounded-md bg-gray-100 dark:bg-gray-700/60 text-gray-700 dark:text-gray-200">
            <span>Customize</span>
            <svg class="w-4 h-4 opacity-70" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true"><path fill-rule="evenodd" d="M5.23 7.21a.75.75 0 011.06.02L10 10.94l3.71-3.71a.75.75 0 011.08 1.04l-4.25 4.25a.75.75 0 01-1.08 0L5.21 8.27a.75.75 0 01.02-1.06z" clip-rule="evenodd"/></svg>
          </button>
          <div id="palette-customize" class="mt-2 hidden space-y-2">
            <!-- Accordion section template: header button + content -->
            <!-- Layout & Structure -->
            <div class="bg-white/70 dark:bg-gray-800/50 backdrop-blur rounded-lg border border-gray-200/60 dark:border-gray-700/50">
              <button data-pc-acc="layout" class="w-full text-left px-3 py-2 text-[12px] font-semibold text-gray-700 dark:text-gray-200 flex items-center justify-between">
                <span>Layout & Structure</span>
                <svg class="w-4 h-4 shrink-0" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true"><path fill-rule="evenodd" d="M5.23 7.21a.75.75 0 011.06.02L10 10.94l3.71-3.71a.75.75 0 011.08 1.04l-4.25 4.25a.75.75 0 01-1.08 0L5.21 8.27a.75.75 0 01.02-1.06z" clip-rule="evenodd"/></svg>
              </button>
              <div data-pc-panel="layout" class="px-3 pb-3 space-y-2">
                <div>
                  <label class="block text-[11px] text-gray-600 dark:text-gray-300 mb-1">Layout</label>
                  <select id="pc-layout" class="w-full px-2 py-1.5 text-xs rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100">
                    <option value="one-column">One Column</option>
                    <option value="two-column">Two Column</option>
                  </select>
                </div>
                <div class="grid grid-cols-2 gap-2">
                  <div>
                    <label class="block text-[11px] text-gray-600 dark:text-gray-300 mb-1">Margins</label>
                    <select id="pc-margins" class="w-full px-2 py-1.5 text-xs rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100">
                      <option value="narrow">Narrow</option>
                      <option value="normal">Normal</option>
                      <option value="wide">Wide</option>
                    </select>
                  </div>
                  <div>
                    <label class="block text-[11px] text-gray-600 dark:text-gray-300 mb-1">Section Spacing</label>
                    <select id="pc-section-spacing" class="w-full px-2 py-1.5 text-xs rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100">
                      <option value="compact">Compact</option>
                      <option value="normal">Normal</option>
                      <option value="relaxed">Relaxed</option>
                      <option value="extra-relaxed">Extra Relaxed</option>
                    </select>
                  </div>
                </div>
              </div>
            </div>
            <!-- Typography -->
            <div class="bg-white/70 dark:bg-gray-800/50 backdrop-blur rounded-lg border border-gray-200/60 dark:border-gray-700/50">
              <button data-pc-acc="typography" class="w-full text-left px-3 py-2 text-[12px] font-semibold text-gray-700 dark:text-gray-200 flex items-center justify-between">
                <span>Typography</span>
                <svg class="w-4 h-4 shrink-0" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true"><path fill-rule="evenodd" d="M5.23 7.21a.75.75 0 011.06.02L10 10.94l3.71-3.71a.75.75 0 011.08 1.04l-4.25 4.25a.75.75 0 01-1.08 0L5.21 8.27a.75.75 0 01.02-1.06z" clip-rule="evenodd"/></svg>
              </button>
              <div data-pc-panel="typography" class="px-3 pb-3 space-y-2 hidden">
                <div>
                  <label class="block text-[11px] text-gray-600 dark:text-gray-300 mb-1">Font</label>
                  <select id="pc-font" class="w-full px-2 py-1.5 text-xs rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100">
                    <option value="Arial, sans-serif">Arial</option>
                    <option value="'Times New Roman', serif">Times New Roman</option>
                    <option value="Calibri, sans-serif">Calibri</option>
                    <option value="Helvetica, Arial, sans-serif">Helvetica</option>
                    <option value="Georgia, serif">Georgia</option>
                    <option value="'Garamond', serif">Garamond</option>
                    <option value="'Roboto', sans-serif">Roboto</option>
                    <option value="'Lato', sans-serif">Lato</option>
                    <option value="'Open Sans', sans-serif">Open Sans</option>
                    <option value="'Merriweather', serif">Merriweather</option>
                    <option value="'Source Sans Pro', sans-serif">Source Sans Pro</option>
                  </select>
                </div>
                <div class="grid grid-cols-2 gap-2">
                  <div>
                    <label class="block text-[11px] text-gray-600 dark:text-gray-300 mb-1">Body Size</label>
                    <select id="pc-font-size" class="w-full px-2 py-1.5 text-xs rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100">
                      <option value="10pt">10pt</option>
                      <option value="11pt">11pt</option>
                      <option value="12pt">12pt</option>
                    </select>
                  </div>
                  <div>
                    <label class="block text-[11px] text-gray-600 dark:text-gray-300 mb-1">Line Spacing</label>
                    <select id="pc-line" class="w-full px-2 py-1.5 text-xs rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100">
                      <option value="1.0">Single</option>
                      <option value="1.05">1.05</option>
                      <option value="1.15">1.15</option>
                      <option value="1.2">1.2</option>
                      <option value="1.3">1.3</option>
                      <option value="1.5">1.5</option>
                      <option value="2.0">Double</option>
                    </select>
                  </div>
                </div>
              </div>
            </div>
            <!-- Content -->
            <div class="bg-white/70 dark:bg-gray-800/50 backdrop-blur rounded-lg border border-gray-200/60 dark:border-gray-700/50">
              <button data-pc-acc="content" class="w-full text-left px-3 py-2 text-[12px] font-semibold text-gray-700 dark:text-gray-200 flex items-center justify-between">
                <span>Content</span>
                <svg class="w-4 h-4 shrink-0" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true"><path fill-rule="evenodd" d="M5.23 7.21a.75.75 0 011.06.02L10 10.94l3.71-3.71a.75.75 0 011.08 1.04l-4.25 4.25a.75.75 0 01-1.08 0L5.21 8.27a.75.75 0 01.02-1.06z" clip-rule="evenodd"/></svg>
              </button>
              <div data-pc-panel="content" class="px-3 pb-3 space-y-2 hidden">
                <label class="block text-[11px] text-gray-600 dark:text-gray-300 mb-1">Date Format</label>
                <select id="pc-date" class="w-full px-2 py-1.5 text-xs rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100">
                  <option value="short">Jan 2022</option>
                  <option value="long">January 2022</option>
                  <option value="year-only">2022</option>
                  <option value="full-date">01/2022</option>
                  <option value="range">2020 - 2022</option>
                </select>
              </div>
            </div>
            <!-- Headings -->
            <div class="bg-white/70 dark:bg-gray-800/50 backdrop-blur rounded-lg border border-gray-200/60 dark:border-gray-700/50">
              <button data-pc-acc="headings" class="w-full text-left px-3 py-2 text-[12px] font-semibold text-gray-700 dark:text-gray-200 flex items-center justify-between">
                <span>Heading Styles</span>
                <svg class="w-4 h-4 shrink-0" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true"><path fill-rule="evenodd" d="M5.23 7.21a.75.75 0 011.06.02L10 10.94l3.71-3.71a.75.75 0 011.08 1.04l-4.25 4.25a.75.75 0 01-1.08 0L5.21 8.27a.75.75 0 01.02-1.06z" clip-rule="evenodd"/></svg>
              </button>
              <div data-pc-panel="headings" class="px-3 pb-3 space-y-2 hidden">
                <div class="space-y-1">
                  <label class="flex items-center gap-2 text-[12px]"><input id="pc-heading-underlined" type="checkbox" class="rounded"> <span>Underlined</span></label>
                  <label class="flex items-center gap-2 text-[12px]"><input id="pc-heading-allcaps" type="checkbox" class="rounded"> <span>ALL CAPS</span></label>
                  <label class="flex items-center gap-2 text-[12px]"><input id="pc-heading-rule" type="checkbox" class="rounded" checked> <span>Horizontal Rule</span></label>
                </div>
                <div>
                  <div class="text-[11px] font-medium text-gray-600 dark:text-gray-300 mb-1">Heading Color</div>
                  <div id="pc-heading-colors" class="flex flex-wrap gap-2">
                    <button data-color="default" class="h-6 w-6 rounded-full border-2 bg-black"></button>
                    <button data-color="#4a90e2" class="h-6 w-6 rounded-full border-2 bg-[#4a90e2]"></button>
                    <button data-color="#50e3c2" class="h-6 w-6 rounded-full border-2 bg-[#50e3c2]"></button>
                    <button data-color="#d0021b" class="h-6 w-6 rounded-full border-2 bg-[#d0021b]"></button>
                    <button data-color="#417505" class="h-6 w-6 rounded-full border-2 bg-[#417505]"></button>
                  </div>
                </div>
                <div>
                  <div class="text-[11px] font-medium text-gray-600 dark:text-gray-300 mb-1">Body Text</div>
                  <div id="pc-body-colors" class="flex flex-wrap gap-2">
                    <button data-color="#374151" class="h-6 w-6 rounded-full border-2 bg-[#374151]"></button>
                    <button data-color="#000000" class="h-6 w-6 rounded-full border-2 bg-black"></button>
                  </div>
                </div>
              </div>
            </div>
            <!-- Visibility -->
            <div class="bg-white/70 dark:bg-gray-800/50 backdrop-blur rounded-lg border border-gray-200/60 dark:border-gray-700/50">
              <button data-pc-acc="visibility" class="w-full text-left px-3 py-2 text-[12px] font-semibold text-gray-700 dark:text-gray-200 flex items-center justify-between">
                <span>Section Visibility</span>
                <svg class="w-4 h-4 shrink-0" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true"><path fill-rule="evenodd" d="M5.23 7.21a.75.75 0 011.06.02L10 10.94l3.71-3.71a.75.75 0 011.08 1.04l-4.25 4.25a.75.75 0 01-1.08 0L5.21 8.27a.75.75 0 01.02-1.06z" clip-rule="evenodd"/></svg>
              </button>
              <div data-pc-panel="visibility" class="px-3 pb-3 space-y-1 hidden">
                <label class="flex items-center justify-between text-[12px] py-1">
                  <span>Summary</span><input id="pc-vis-summary" type="checkbox" class="rounded" checked>
                </label>
                <label class="flex items-center justify-between text-[12px] py-1">
                  <span>Work Experience</span><input id="pc-vis-work" type="checkbox" class="rounded" checked>
                </label>
                <label class="flex items-center justify-between text-[12px] py-1">
                  <span>Education</span><input id="pc-vis-edu" type="checkbox" class="rounded" checked>
                </label>
                <label class="flex items-center justify-between text-[12px] py-1">
                  <span>Skills</span><input id="pc-vis-skills" type="checkbox" class="rounded" checked>
                </label>
                <div class="pt-1">
                  <button id="pc-reset" class="text-[12px] font-semibold text-indigo-600">Reset Styles</button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </aside>

    <!-- Preview content -->
<div>
    <!-- Action buttons container - positioned above the preview -->
    <div class="mb-4 text-center">
        <div id="preview-buttons" class="flex flex-wrap items-center justify-center gap-2 bg-white/80 dark:bg-gray-800/60 backdrop-blur rounded-2xl px-2 py-2 mx-auto ring-1 ring-gray-200/70 dark:ring-gray-700/60 shadow-sm" style="max-width: fit-content;">
            <button id="sample-data-btn" class="inline-flex items-center gap-1.5 text-xs font-semibold px-3.5 py-2 rounded-full transition-all duration-200 hover:shadow hover:-translate-y-0.5 bg-emerald-50 text-emerald-700 dark:bg-emerald-900/30 dark:text-emerald-300 hover:bg-emerald-100 dark:hover:bg-emerald-800/40">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2" aria-hidden="true">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                <span>Sample</span>
            </button>
            <button id="clear-data-btn" class="inline-flex items-center gap-1.5 text-xs font-semibold px-3.5 py-2 rounded-full transition-all duration-200 hover:shadow hover:-translate-y-0.5 bg-rose-50 text-rose-700 dark:bg-rose-900/30 dark:text-rose-300 hover:bg-rose-100 dark:hover:bg-rose-800/40">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2" aria-hidden="true">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                </svg>
                <span>Clear</span>
            </button>
            <button id="download-pdf-btn" class="inline-flex items-center gap-1.5 text-xs font-semibold px-3.5 py-2 rounded-full transition-all duration-200 hover:shadow hover:-translate-y-0.5 bg-indigo-50 text-indigo-700 dark:bg-indigo-900/30 dark:text-indigo-300 hover:bg-indigo-100 dark:hover:bg-indigo-800/40">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2" aria-hidden="true">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                </svg>
                <span>Download</span>
            </button>
        </div>
    </div>

    <!-- Preview content -->
    <div id="resume-preview-content" class="prose prose-sm dark:prose-invert max-w-full mx-auto" data-template="harvard">
        <!-- The formatted resume content will be rendered here -->
    </div>
    <!-- Floating toolbar -->
    <FloatingToolbar />
</div>

    <style>
        /* Modernized sheet styling */
        #resume-preview-content {
            /* Responsive web-optimized layout - no fixed aspect ratio */
            width: 100%;
            max-width: 700px; /* Smaller max width for better web viewing */
            min-width: 320px; /* Minimum width for mobile */
            min-height: auto; /* Let content determine height */
            margin-left: auto;
            margin-right: auto;
            background:
                radial-gradient(1200px 600px at 0% 0%, rgba(17,24,39,0.03), transparent 60%),
                #fff;
            border: 1px solid #e5e7eb;
            border-radius: 0.75rem;
            box-shadow:
                0 1px 2px rgba(0,0,0,0.05),
                0 8px 24px rgba(0,0,0,0.08);
            padding: 1rem; /* Responsive padding */
            transition: transform 160ms ease, box-shadow 200ms ease;
            will-change: transform; /* avoid layout jitter that can cause overflow */
            overflow: visible; /* Ensure controls aren't clipped */
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            #resume-preview-content {
                max-width: 600px;
                border-radius: 0.5rem;
                padding: 0.75rem;
                min-width: 280px;
            }
        }

        @media (max-width: 480px) {
            #resume-preview-content {
                max-width: 100%;
                border-radius: 0.25rem;
                padding: 0.5rem;
                min-width: 260px;
                margin: 0 0.5rem; /* Add side margins on mobile */
            }
        }

        /* Toolbar container */
        #preview-buttons {
            background: rgba(255, 255, 255, 0.88);
            backdrop-filter: blur(8px);
            border: 1px solid #e5e7eb;
            border-radius: 1rem;
            box-shadow:
                0 1px 2px rgba(0,0,0,0.05),
                0 8px 24px rgba(0,0,0,0.08);
        }
        .dark #preview-buttons {
            background: rgba(31, 41, 55, 0.72);
            border-color: #374151;
            box-shadow:
                0 1px 2px rgba(0,0,0,0.3),
                0 8px 24px rgba(0,0,0,0.35);
        }

        /* Sections palette visibility when Customizer is open (desktop) */
        #sections-palette {
          transition: opacity 0.25s ease, transform 0.25s ease;
        }
        .customizer-open-root #sections-palette {
          opacity: 0;
          transform: translateX(-8px);
          pointer-events: none;
        }

        /* Sections palette rows with separate scroll and toggle buttons */
        .section-row {
          display: grid;
          grid-template-columns: auto 1fr auto;
          gap: 0.4rem;
          align-items: center;
          transition: all 0.2s ease;
          position: relative;
          padding: 0.25rem;
          border-radius: 0.375rem;
        }
        .drag-handle {
          color: #9ca3af;
          cursor: grab;
          padding: 0.375rem 0.25rem;
          border-radius: 0.375rem;
          transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
          user-select: none;
          display: flex;
          align-items: center;
          justify-content: center;
          width: 20px;
          height: 20px;
          position: relative;
          background: transparent;
          border: 1px solid transparent;
        }

        .drag-handle::before {
          content: '';
          width: 12px;
          height: 12px;
          background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke-width='2' stroke='%239ca3af'%3e%3cpath stroke-linecap='round' stroke-linejoin='round' d='M8 9h8M8 15h8'/%3e%3c/svg%3e");
          background-repeat: no-repeat;
          background-position: center;
          background-size: contain;
          transition: all 0.2s ease;
        }

        .section-row {
          position: relative;
          display: flex;
          align-items: center;
          gap: 0.5rem;
          padding: 0.25rem;
          border-radius: 0.5rem;
          transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
          background: transparent;
        }

        .section-row:hover {
          background: rgba(99,102,241,0.04);
          transform: translateY(-1px);
          box-shadow: 0 2px 8px rgba(0,0,0,0.06);
        }

        .section-row:hover .drag-handle {
          color: #4f46e5;
          background: rgba(99,102,241,0.1);
          border-color: rgba(99,102,241,0.2);
          transform: scale(1.05);
        }

        .section-row:hover .drag-handle::before {
          background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke-width='2' stroke='%234f46e5'%3e%3cpath stroke-linecap='round' stroke-linejoin='round' d='M8 9h8M8 15h8'/%3e%3c/svg%3e");
        }

        .section-row:active .drag-handle {
          cursor: grabbing;
          transform: scale(0.95);
        }

        .section-row.dragging {
          opacity: 0.7;
          transform: scale(1.02) rotate(1deg);
          background: rgba(99,102,241,0.08);
          box-shadow: 0 8px 25px rgba(0,0,0,0.15), 0 4px 12px rgba(99,102,241,0.2);
          z-index: 1000;
          border: 1px solid rgba(99,102,241,0.3);
        }

        .section-row.drag-over {
          background: rgba(99,102,241,0.06);
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(99,102,241,0.15);
        }

        .section-row.drag-over::after {
          content: '';
          position: absolute;
          top: -4px;
          left: -4px;
          right: -4px;
          height: 4px;
          background: linear-gradient(90deg, #4f46e5, #7c3aed);
          border-radius: 2px;
          box-shadow: 0 0 12px rgba(79, 70, 229, 0.5);
          animation: dragOverPulse 1.5s ease-in-out infinite;
        }

        .drag-placeholder {
          height: 40px;
          background: linear-gradient(135deg, rgba(99,102,241,0.1), rgba(124,58,237,0.1));
          border: 2px dashed rgba(99,102,241,0.4);
          border-radius: 0.5rem;
          margin: 0.5rem 0;
          opacity: 0.9;
          position: relative;
          overflow: hidden;
          animation: placeholderPulse 2s ease-in-out infinite;
        }

        .drag-placeholder::before {
          content: 'Drop here';
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          font-size: 0.75rem;
          font-weight: 500;
          color: rgba(99,102,241,0.7);
          pointer-events: none;
        }

        .drag-placeholder::after {
          content: '';
          position: absolute;
          top: 0;
          left: -100%;
          width: 100%;
          height: 100%;
          background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
          animation: shimmer 2s ease-in-out infinite;
        }

        @keyframes dragOverPulse {
          0%, 100% {
            opacity: 0.8;
            transform: scaleX(0.98);
          }
          50% {
            opacity: 1;
            transform: scaleX(1);
          }
        }

        @keyframes placeholderPulse {
          0%, 100% {
            opacity: 0.9;
            border-color: rgba(99,102,241,0.4);
          }
          50% {
            opacity: 0.7;
            border-color: rgba(99,102,241,0.6);
          }
        }

        @keyframes shimmer {
          0% { left: -100%; }
          100% { left: 100%; }
        }
        .section-chip {
          position: relative;
          display: block;
          width: 100%;
          text-align: left;
          font-size: 0.875rem;
          font-weight: 500;
          line-height: 1.2;
          padding: 0.75rem 1rem;
          border-radius: 0.5rem;
          color: #374151;
          background: rgba(249, 250, 251, 0.8);
          border: 1px solid rgba(229, 231, 235, 0.6);
          transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
          cursor: pointer;
          overflow: hidden;
        }

        .section-chip::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          width: 3px;
          height: 100%;
          background: transparent;
          transition: all 0.2s ease;
        }

        .section-chip:hover {
          background: rgba(99,102,241,0.05);
          border-color: rgba(99,102,241,0.2);
          color: #4f46e5;
          transform: translateX(2px);
          box-shadow: 0 2px 8px rgba(0,0,0,0.06);
        }

        .section-chip:hover::before {
          background: linear-gradient(180deg, #4f46e5, #7c3aed);
        }

        .section-chip.is-active {
          background: rgba(99,102,241,0.1);
          border-color: rgba(99,102,241,0.3);
          color: #4f46e5;
          font-weight: 600;
        }

        .section-chip.is-active::before {
          background: linear-gradient(180deg, #4f46e5, #7c3aed);
        }

        .dark .section-chip {
          color: #d1d5db;
          background: rgba(31, 41, 55, 0.6);
          border-color: rgba(75, 85, 99, 0.4);
        }

        .dark .section-chip:hover {
          background: rgba(99,102,241,0.1);
          border-color: rgba(99,102,241,0.3);
          color: #a5b4fc;
        }

        .dark .section-chip.is-active {
          background: rgba(99,102,241,0.15);
          border-color: rgba(99,102,241,0.4);
          color: #a5b4fc;
        }
        .section-chip:hover {
          background: rgba(99,102,241,0.08);
          border-color: rgba(99,102,241,0.18);
        }
        .section-chip.is-active {
          background: rgba(16,185,129,0.1);
          border-color: rgba(16,185,129,0.4);
          color: #065f46;
        }
        .dark .section-chip { color: #e5e7eb; }
        .dark .section-chip:hover { background: rgba(99,102,241,0.15); border-color: rgba(99,102,241,0.3); }
        .dark .section-chip.is-active { background: rgba(16,185,129,0.18); border-color: rgba(16,185,129,0.5); color: #d1fae5; }

        .chip-act {
          position: relative;
          display: flex;
          align-items: center;
          justify-content: center;
          width: 2rem;
          height: 2rem;
          font-size: 0.875rem;
          font-weight: 600;
          border-radius: 0.375rem;
          border: 1px solid rgba(99,102,241,0.25);
          color: #4f46e5;
          background: rgba(99,102,241,0.06);
          transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
          cursor: pointer;
          overflow: hidden;
        }

        .chip-act::before {
          content: '';
          position: absolute;
          top: 50%;
          left: 50%;
          width: 0;
          height: 0;
          background: currentColor;
          border-radius: 50%;
          transform: translate(-50%, -50%);
          transition: all 0.2s ease;
          opacity: 0.1;
        }

        .chip-act:hover::before {
          width: 100%;
          height: 100%;
        }

        .chip-act:hover {
          background: rgba(99,102,241,0.12);
          border-color: rgba(99,102,241,0.4);
          transform: scale(1.05);
          box-shadow: 0 2px 8px rgba(99,102,241,0.15);
        }

        .chip-act:active {
          transform: scale(0.95);
        }

        .chip-act[data-mode="add"] {
          color: #059669;
          border-color: rgba(16,185,129,0.3);
          background: rgba(16,185,129,0.06);
        }

        .chip-act[data-mode="add"]:hover {
          background: rgba(16,185,129,0.12);
          border-color: rgba(16,185,129,0.4);
          color: #047857;
        }

        .chip-act[data-mode="remove"] {
          color: #b91c1c;
          border-color: rgba(239,68,68,0.35);
          background: rgba(239,68,68,0.08);
        }

        .chip-act[data-mode="remove"]:hover {
          background: rgba(239,68,68,0.14);
          border-color: rgba(239,68,68,0.5);
          color: #991b1b;
        }

        .dark .chip-act {
          color: #a5b4fc;
          border-color: rgba(129,140,248,0.35);
          background: rgba(129,140,248,0.12);
        }

        .dark .chip-act:hover {
          background: rgba(129,140,248,0.2);
          border-color: rgba(129,140,248,0.5);
        }

        .dark .chip-act[data-mode="add"] {
          color: #34d399;
          border-color: rgba(52,211,153,0.3);
          background: rgba(52,211,153,0.1);
        }

        .dark .chip-act[data-mode="add"]:hover {
          background: rgba(52,211,153,0.15);
          border-color: rgba(52,211,153,0.4);
        }

        .dark .chip-act[data-mode="remove"] {
          color: #fecaca;
          border-color: rgba(248,113,113,0.4);
          background: rgba(248,113,113,0.15);
        }

        .dark .chip-act[data-mode="remove"]:hover {
          background: rgba(248,113,113,0.22);
          border-color: rgba(248,113,113,0.55);
        }

        /* Button micro-interactions without changing markup */
        #preview-buttons button {
            border-radius: 9999px;
            transition: transform 0.18s ease, box-shadow 0.18s ease, background-color 0.2s ease, color 0.2s ease;
            will-change: transform;
        }
        #preview-buttons button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 18px rgba(0,0,0,0.08);
        }

        /* Optional two-column layout support hook */
        .two-column {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 2rem;
        }
        .two-column .main-content { grid-column: 2 / 3; }
        .two-column .sidebar { grid-column: 1 / 2; }

        /* Draggable item affordances */
        .item-container {
            position: relative;
            overflow: visible;
        }
        .item-toolbar {
            position: absolute;
            top: -10px;
            right: -10px;
            display: none;
            background-color: white;
            border-radius: 0.5rem;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
            padding: 0.25rem;
            z-index: 10;
        }
        .item-container:hover .item-toolbar {
            display: flex;
            gap: 0.25rem;
        }
        .tb {
            border: none;
            background: none;
            cursor: pointer;
            padding: 0.5rem;
            border-radius: 0.25rem;
        }
        .tb:hover { background-color: #f3f4f6; }



        /* Smooth reordering animations */
        .reordering-item {
            transition: transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        .slide-up-smooth {
            animation: slideUpSmooth 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        .slide-down-smooth {
            animation: slideDownSmooth 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        @keyframes slideUpSmooth {
            0% { transform: translateY(100%); opacity: 0.7; }
            100% { transform: translateY(0); opacity: 1; }
        }

        @keyframes slideDownSmooth {
            0% { transform: translateY(-100%); opacity: 0.7; }
            100% { transform: translateY(0); opacity: 1; }
        }

        /* Enhanced drag target highlight */
        .drag-over {
            outline: 2px solid rgba(99,102,241,0.4);
            outline-offset: 4px;
            border-radius: 0.5rem;
            background: rgba(99,102,241,0.06);
            transform: scale(1.02) translateY(-2px);
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 8px 25px rgba(99,102,241,0.15);
        }

        /* Enhanced draggable item animations */
        .draggable {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            transform-origin: center;
        }

        .draggable:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.1);
        }

        .dragging {
            opacity: 0.7;
            transform: rotate(1deg) scale(1.02);
            z-index: 1000;
            box-shadow: 0 12px 35px rgba(0,0,0,0.2), 0 6px 15px rgba(99,102,241,0.15);
            border: 1px solid rgba(99,102,241,0.3);
        }

        /* Enhanced drag placeholder for preview */
        .drag-placeholder {
            background: linear-gradient(135deg, rgba(99,102,241,0.08), rgba(124,58,237,0.08));
            border: 2px dashed rgba(99,102,241,0.4);
            border-radius: 0.75rem;
            height: 60px;
            margin: 1rem 0;
            opacity: 0.9;
            position: relative;
            overflow: hidden;
            animation: previewPlaceholderPulse 2s ease-in-out infinite;
        }

        .drag-placeholder::before {
            content: 'Drop section here';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 0.875rem;
            font-weight: 500;
            color: rgba(99,102,241,0.7);
            pointer-events: none;
        }

        .drag-placeholder::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            animation: previewShimmer 2.5s ease-in-out infinite;
        }

        @keyframes previewPlaceholderPulse {
            0%, 100% {
                opacity: 0.9;
                border-color: rgba(99,102,241,0.4);
                transform: scale(1);
            }
            50% {
                opacity: 0.7;
                border-color: rgba(99,102,241,0.6);
                transform: scale(1.01);
            }
        }

        @keyframes previewShimmer {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        /* Smooth reordering animations */
        .reordering {
            transition: transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        .slide-up {
            animation: slideUp 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        .slide-down {
            animation: slideDown 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        @keyframes slideUp {
            from {
                transform: translateY(100%);
                opacity: 0.8;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        @keyframes slideDown {
            from {
                transform: translateY(-100%);
                opacity: 0.8;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        /* Enhanced drop zone indicator */
        .drop-zone-active {
            position: relative;
        }

        .drop-zone-active::before {
            content: '';
            position: absolute;
            top: -6px;
            left: -4px;
            right: -4px;
            height: 6px;
            background: linear-gradient(90deg, #6366f1, #8b5cf6);
            border-radius: 3px;
            animation: dropZonePulse 1.5s ease-in-out infinite;
            box-shadow: 0 0 15px rgba(99,102,241,0.4);
        }

        @keyframes dropZonePulse {
            0%, 100% {
                opacity: 0.7;
                transform: scaleX(0.95) scaleY(0.8);
            }
            50% {
                opacity: 1;
                transform: scaleX(1) scaleY(1);
            }
        }

        /* Selected item highlight (like Enhancv selection) */
        .item-selected {
            outline: 2px solid #10b981; /* emerald-500 */
            outline-offset: 2px;
            border-radius: 0.5rem;
            background: rgba(16,185,129,0.04);
            transition: outline-color 0.2s ease, background-color 0.2s ease;
        }
        .dark .item-selected {
            outline-color: #34d399; /* emerald-400 */
            background: rgba(16,185,129,0.08);
        }

        /* Hover selection affordance for entries and bullets */
        #resume-preview-content .item-container[data-work-id],
        #resume-preview-content .item-container[data-edu-id] {
            border-radius: 0.5rem;
            transition: outline-color 0.18s ease, box-shadow 0.18s ease, background-color 0.18s ease, transform 0.18s ease;
        }
        /* Light hover border for Experience/Education entries (not while actively selected) */
        #resume-preview-content .item-container[data-work-id]:hover:not(.item-selected),
        #resume-preview-content .item-container[data-edu-id]:hover:not(.item-selected) {
            outline: 2px solid rgba(99,102,241,0.28); /* indigo-500 @ ~28% */
            outline-offset: 2px;
            background: rgba(99,102,241,0.04);
        }
        .dark #resume-preview-content .item-container[data-work-id]:hover:not(.item-selected),
        .dark #resume-preview-content .item-container[data-edu-id]:hover:not(.item-selected) {
            outline-color: rgba(129,140,248,0.38); /* indigo-400-ish */
            background: rgba(99,102,241,0.08);
        }

        /* Subtle hover affordance for bullet items */
        #resume-preview-content li[contenteditable="true"][data-bullet-id] {
            border-radius: 0.25rem;
            transition: outline-color 0.18s ease, background-color 0.18s ease;
        }
        #resume-preview-content li[contenteditable="true"][data-bullet-id]:hover:not(:focus) {
            outline: 1.5px dashed rgba(99,102,241,0.35);
            outline-offset: 3px;
            background: rgba(99,102,241,0.05);
        }
        .dark #resume-preview-content li[contenteditable="true"][data-bullet-id]:hover:not(:focus) {
            outline-color: rgba(129,140,248,0.45);
            background: rgba(99,102,241,0.09);
        }

        /* Hover affordance for entire sections (Experience/Education containers) */
        #resume-preview-content .item-container[data-section-type] {
            border-radius: 0.5rem;
            transition: outline-color 0.18s ease, background-color 0.18s ease, transform 0.18s ease;
            cursor: grab;
            position: relative;
        }
        #resume-preview-content .item-container[data-section-type]:hover::after {
            content: '';
            position: absolute;
            top: 0.5rem;
            right: 0.5rem;
            width: 20px;
            height: 20px;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke-width='2' stroke='%239ca3af'%3e%3cpath stroke-linecap='round' stroke-linejoin='round' d='M8 9h8M8 15h8'/%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: center;
            background-size: 12px 12px;
            background-color: rgba(255, 255, 255, 0.95);
            border-radius: 0.375rem;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            pointer-events: none;
            user-select: none;
            transition: all 0.2s ease;
            animation: dragHandleFadeIn 0.2s ease-out;
        }

        @keyframes dragHandleFadeIn {
            from {
                opacity: 0;
                transform: scale(0.8);
            }
            to {
                opacity: 1;
                transform: scale(1);
            }
        }

        .dark #resume-preview-content .item-container[data-section-type]:hover::after {
            background-color: rgba(31, 41, 55, 0.95);
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke-width='2' stroke='%23d1d5db'%3e%3cpath stroke-linecap='round' stroke-linejoin='round' d='M8 9h8M8 15h8'/%3e%3c/svg%3e");
        }

        #resume-preview-content .item-container[data-section-type]:active {
            cursor: grabbing;
        }

        #resume-preview-content .item-container[data-section-type].dragging {
            opacity: 0.7;
            transform: scale(1.02) rotate(1deg);
            background: rgba(99,102,241,0.08);
            box-shadow: 0 12px 35px rgba(0,0,0,0.2), 0 6px 15px rgba(99,102,241,0.15);
            z-index: 1000;
            border: 1px solid rgba(99,102,241,0.3);
        }

        #resume-preview-content .item-container[data-section-type].drag-over {
            background: rgba(99,102,241,0.06);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(99,102,241,0.15);
        }

        #resume-preview-content .item-container[data-section-type].drag-over::before {
            content: '';
            position: absolute;
            top: -6px;
            left: -8px;
            right: -8px;
            height: 6px;
            background: linear-gradient(90deg, #4f46e5, #7c3aed);
            border-radius: 3px;
            box-shadow: 0 0 15px rgba(79, 70, 229, 0.5);
            animation: previewDragOverPulse 1.5s ease-in-out infinite;
        }

        @keyframes previewDragOverPulse {
            0%, 100% {
                opacity: 0.8;
                transform: scaleX(0.95);
            }
            50% {
                opacity: 1;
                transform: scaleX(1);
            }
        }

        #resume-preview-content .drag-placeholder {
            height: 60px;
            background: linear-gradient(135deg, rgba(99,102,241,0.08), rgba(124,58,237,0.08));
            border: 2px dashed rgba(99,102,241,0.4);
            border-radius: 0.75rem;
            margin: 1rem 0;
            opacity: 0.9;
            position: relative;
            overflow: hidden;
            animation: previewPlaceholderPulse 2s ease-in-out infinite;
        }

        #resume-preview-content .drag-placeholder::before {
            content: 'Drop section here';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 0.875rem;
            font-weight: 500;
            color: rgba(99,102,241,0.7);
            pointer-events: none;
        }

        #resume-preview-content .drag-placeholder::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            animation: previewShimmer 2.5s ease-in-out infinite;
            animation: pulse 1.5s ease-in-out infinite;
        }
        #resume-preview-content .item-container[data-section-type]:hover:not(.item-selected) {
            outline: 2px solid rgba(99,102,241,0.28); /* indigo-500 @ ~28% */
            outline-offset: 2px;
            background: rgba(99,102,241,0.04);
        }
        .dark #resume-preview-content .item-container[data-section-type]:hover:not(.item-selected) {
            outline-color: rgba(129,140,248,0.38); /* indigo-400-ish */
            background: rgba(99,102,241,0.08);
        }

        /* Editable field styling */
        [contenteditable="true"] {
            transition: background-color 0.2s ease, box-shadow 0.2s ease;
            border-radius: 2px;
            padding: 1px 2px;
            margin: -1px -2px;
        }
        [contenteditable="true"]:focus {
            background-color: rgba(59, 130, 246, 0.1);
            box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
            outline: none;
        }
        [contenteditable="true"]:hover:not(:focus) {
            background-color: rgba(156, 163, 175, 0.1);
        }
        .dark [contenteditable="true"]:focus {
            background-color: rgba(59, 130, 246, 0.15);
            box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.3);
        }
        .dark [contenteditable="true"]:hover:not(:focus) {
            background-color: rgba(156, 163, 175, 0.15);
        }

        /* Placeholder styling for empty editable fields */
        [contenteditable="true"][data-placeholder]:empty::before {
            content: attr(data-placeholder);
            color: #9ca3af;
            font-style: italic;
        }
        .dark [contenteditable="true"][data-placeholder]:empty::before {
            color: #6b7280;
        }

        /* Hide legacy inline add buttons when section already has entries */
        #section-experience:has([data-work-id]) [data-action="add-job"] { display: none; }
        #section-education:has([data-edu-id]) [data-action="add-edu"] { display: none; }
        #resume-preview-content:has([data-custom-id]) [data-action="add-custom"] { display: none; }



        @keyframes pop-in {
            from { opacity: 0; transform: translateY(-4px) scale(0.98); }
            to   { opacity: 1; transform: translateY(0) scale(1); }
        }





        /* Print: isolate resume with natural page breaks */
        @media print {
            /* Use page margins so every page (including subsequent pages) has identical top/bottom/side spacing */
            @page { size: A4; margin: 0.75in; }

            /* Hide interactive UI */
            #preview-buttons,
            [data-action],
            .cursor-grab,
            #customize-toggle-buttons,
            #toggle-fit-btn,
            .inline-popover,
            #floating-toolbar,
            #inline-toolbar {
                display: none !important;
            }

            /* Show only the resume content */
            body > *:not(#resume-preview-content) {
                display: none !important;
            }
            #resume-preview-content {
                position: static !important;
                width: auto !important;
                margin: 0 !important;
                /* Remove element padding for print; rely on @page margins for all pages */
                padding: 0 !important;
                border: none !important;
                box-shadow: none !important;
                transform: none !important;
                background: #fff !important;
            }

            /* Avoid breaking items in awkward places */
            #resume-preview-content section,
            #resume-preview-content header,
            #resume-preview-content .item-container,
            #resume-preview-content ul,
            #resume-preview-content li {
                break-inside: avoid-page;
                page-break-inside: avoid;
            }
        }
    </style>

    <script>
        import { resumeData, moveSection, clearResumeData, loadSampleData, setSectionVisibility, addCustomSectionWithTitle, removeCustomSectionsByTitle } from '../../lib/resumeBuilderService';

        // Ambient type declarations for CDN libraries
        declare global {
            interface Window {
                jspdf: any;
                html2canvas: any;
            }
        }

        document.addEventListener('DOMContentLoaded', () => {
            const previewContent = document.getElementById('resume-preview-content');
            let previewWrapper = document.getElementById('resume-preview-content-wrapper') as HTMLElement | null;

            // Function to render sidebar sections based on sectionsOrder
            function renderSidebarSections(data: any) {
                const sectionsContainer = document.getElementById('sections-default');
                if (!sectionsContainer) return;

                const sectionLabels = {
                    summary: 'Summary',
                    workExperience: 'Experience',
                    education: 'Education',
                    skills: 'Skills'
                };

                const sectionsOrder = data.sectionsOrder || ['summary', 'workExperience', 'education', 'skills'];

                sectionsContainer.innerHTML = '';

                sectionsOrder.forEach((sectionKey: string) => {
                    if (sectionKey === 'customSections') return; // Skip custom sections in main list

                    const label = sectionLabels[sectionKey as keyof typeof sectionLabels];
                    if (!label) return;

                    const isVisible = data.sectionVisibility?.[sectionKey as keyof typeof data.sectionVisibility] ?? true;

                    const sectionRow = document.createElement('div');
                    sectionRow.className = 'section-row';
                    sectionRow.setAttribute('data-section-row', sectionKey);
                    sectionRow.draggable = true;

                    sectionRow.innerHTML = `
                        <div class="drag-handle"></div>
                        <button class="section-chip chip-main ${isVisible ? 'is-active' : ''}" data-section="${sectionKey}" data-section-chip="${sectionKey}" aria-label="Scroll to ${label}">${label}</button>
                        <button class="chip-act" data-action="toggle-default" data-key="${sectionKey}" data-mode="${isVisible ? 'remove' : 'add'}" aria-label="${isVisible ? 'Remove' : 'Add'} ${label}">${isVisible ? '−' : '+'}</button>
                    `;

                    sectionsContainer.appendChild(sectionRow);
                });

                // Add event listeners for section buttons
                sectionsContainer.querySelectorAll<HTMLButtonElement>('[data-section-chip]').forEach(btn => {
                    btn.addEventListener('click', () => {
                        const key = btn.dataset.sectionChip as 'summary'|'workExperience'|'education'|'skills';
                        const isOn = data.sectionVisibility?.[key] ?? true;
                        if (isOn) {
                            scrollToSection(key, 'default');
                        }
                    });
                });

                sectionsContainer.querySelectorAll<HTMLButtonElement>('.chip-act[data-action="toggle-default"]').forEach(btn => {
                    btn.addEventListener('click', (e) => {
                        e.stopPropagation();
                        const key = (btn.dataset.key as 'summary'|'workExperience'|'education'|'skills');
                        import('../../lib/resumeBuilderService').then(m => {
                            const state = m.resumeData.get();
                            const isOn = !!state.sectionVisibility?.[key];
                            if (isOn) {
                                m.setSectionVisibility(key, false);
                            } else {
                                m.setSectionVisibility(key, true);
                                setTimeout(() => scrollToSection(key, 'default'), 100);
                            }
                        });
                    });
                });

                // Add drag and drop event listeners to sidebar sections
                addSidebarDragAndDrop();
            }

            // Function to add drag and drop functionality to sidebar sections
            function addSidebarDragAndDrop() {
                const sectionsContainer = document.getElementById('sections-default');
                if (!sectionsContainer) return;

                const sectionRows = sectionsContainer.querySelectorAll('.section-row');
                let draggedElement: HTMLElement | null = null;
                let placeholder: HTMLElement | null = null;

                const createPlaceholder = () => {
                    const div = document.createElement('div');
                    div.className = 'drag-placeholder';
                    return div;
                };

                const removePlaceholder = () => {
                    if (placeholder && placeholder.parentNode) {
                        placeholder.parentNode.removeChild(placeholder);
                        placeholder = null;
                    }
                };

                sectionRows.forEach((row) => {
                    const element = row as HTMLElement;
                    const dragHandle = element.querySelector('.drag-handle') as HTMLElement;

                    // Make the drag handle the primary drag trigger
                    if (dragHandle) {
                        dragHandle.addEventListener('mousedown', () => {
                            element.draggable = true;
                        });
                    }

                    element.addEventListener('dragstart', (e) => {
                        draggedElement = element;
                        element.classList.add('dragging');
                        e.dataTransfer!.effectAllowed = 'move';
                        e.dataTransfer!.setData('text/html', element.outerHTML);

                        // Create and show placeholder
                        placeholder = createPlaceholder();
                    });

                    element.addEventListener('dragend', () => {
                        element.classList.remove('dragging');
                        draggedElement = null;
                        removePlaceholder();
                        // Remove all drag-over classes
                        sectionRows.forEach(r => r.classList.remove('drag-over'));
                    });

                    element.addEventListener('dragover', (e) => {
                        e.preventDefault();
                        e.dataTransfer!.dropEffect = 'move';

                        if (draggedElement && element !== draggedElement && placeholder) {
                            const rect = element.getBoundingClientRect();
                            const midY = rect.top + rect.height / 2;
                            const insertBefore = e.clientY < midY;

                            removePlaceholder();
                            placeholder = createPlaceholder();

                            if (insertBefore) {
                                element.parentNode!.insertBefore(placeholder, element);
                            } else {
                                element.parentNode!.insertBefore(placeholder, element.nextSibling);
                            }
                        }
                    });

                    element.addEventListener('dragenter', (e) => {
                        e.preventDefault();
                        if (draggedElement && element !== draggedElement) {
                            element.classList.add('drag-over');
                        }
                    });

                    element.addEventListener('dragleave', (e) => {
                        if (!element.contains(e.relatedTarget as Node)) {
                            element.classList.remove('drag-over');
                        }
                    });

                    element.addEventListener('drop', (e) => {
                        e.preventDefault();
                        element.classList.remove('drag-over');
                        removePlaceholder();

                        if (draggedElement && element !== draggedElement) {
                            const draggedSectionKey = draggedElement.getAttribute('data-section-row');
                            const targetSectionKey = element.getAttribute('data-section-row');

                            if (draggedSectionKey && targetSectionKey) {
                                const rect = element.getBoundingClientRect();
                                const insertBefore = e.clientY < rect.top + rect.height / 2;

                                // Update the sections order
                                import('../../lib/resumeBuilderService').then(m => {
                                    const currentData = m.resumeData.get();
                                    const newOrder = [...currentData.sectionsOrder];
                                    const draggedIndex = newOrder.indexOf(draggedSectionKey);
                                    const targetIndex = newOrder.indexOf(targetSectionKey);

                                    if (draggedIndex !== -1 && targetIndex !== -1) {
                                        // Remove from current position
                                        newOrder.splice(draggedIndex, 1);
                                        // Insert at new position
                                        const insertIndex = insertBefore ? targetIndex : targetIndex + 1;
                                        newOrder.splice(insertIndex > draggedIndex ? insertIndex - 1 : insertIndex, 0, draggedSectionKey);

                                        m.reorderSections(newOrder);
                                    }
                                });
                            }
                        }
                    });
                });
            }

            // Function to add drag and drop functionality to preview sections
            function addPreviewDragAndDrop() {
                const previewContent = document.getElementById('resume-preview-content');
                if (!previewContent) return;

                const sections = previewContent.querySelectorAll('.item-container[data-section-type]:not([data-section-type="header"])');
                let draggedElement: HTMLElement | null = null;
                let placeholder: HTMLElement | null = null;

                const createPlaceholder = () => {
                    const div = document.createElement('div');
                    div.className = 'drag-placeholder';
                    return div;
                };

                const removePlaceholder = () => {
                    if (placeholder && placeholder.parentNode) {
                        placeholder.parentNode.removeChild(placeholder);
                        placeholder = null;
                    }
                };

                sections.forEach((section) => {
                    const element = section as HTMLElement;

                    element.addEventListener('dragstart', (e) => {
                        draggedElement = element;
                        element.classList.add('dragging');
                        e.dataTransfer!.effectAllowed = 'move';
                        e.dataTransfer!.setData('text/html', element.outerHTML);

                        // Create placeholder
                        placeholder = createPlaceholder();
                    });

                    element.addEventListener('dragend', () => {
                        element.classList.remove('dragging');
                        draggedElement = null;
                        removePlaceholder();
                        // Remove all drag-over classes
                        sections.forEach(s => s.classList.remove('drag-over'));
                    });

                    element.addEventListener('dragover', (e) => {
                        e.preventDefault();
                        e.dataTransfer!.dropEffect = 'move';

                        if (draggedElement && element !== draggedElement && placeholder) {
                            const rect = element.getBoundingClientRect();
                            const midY = rect.top + rect.height / 2;
                            const insertBefore = e.clientY < midY;

                            removePlaceholder();
                            placeholder = createPlaceholder();

                            if (insertBefore) {
                                element.parentNode!.insertBefore(placeholder, element);
                            } else {
                                element.parentNode!.insertBefore(placeholder, element.nextSibling);
                            }
                        }
                    });

                    element.addEventListener('dragenter', (e) => {
                        e.preventDefault();
                        if (draggedElement && element !== draggedElement) {
                            element.classList.add('drag-over');
                        }
                    });

                    element.addEventListener('dragleave', (e) => {
                        if (!element.contains(e.relatedTarget as Node)) {
                            element.classList.remove('drag-over');
                        }
                    });

                    element.addEventListener('drop', (e) => {
                        e.preventDefault();
                        element.classList.remove('drag-over');
                        removePlaceholder();

                        if (draggedElement && element !== draggedElement) {
                            const draggedSectionType = draggedElement.getAttribute('data-section-type');
                            const targetSectionType = element.getAttribute('data-section-type');

                            // Map section types to section keys
                            const sectionTypeToKey = {
                                'summary': 'summary',
                                'work': 'workExperience',
                                'edu': 'education',
                                'skills': 'skills',
                                'custom': 'customSections'
                            };

                            const draggedSectionKey = sectionTypeToKey[draggedSectionType as keyof typeof sectionTypeToKey];
                            const targetSectionKey = sectionTypeToKey[targetSectionType as keyof typeof sectionTypeToKey];

                            if (draggedSectionKey && targetSectionKey) {
                                const rect = element.getBoundingClientRect();
                                const insertBefore = e.clientY < rect.top + rect.height / 2;

                                // Update the sections order
                                import('../../lib/resumeBuilderService').then(m => {
                                    const currentData = m.resumeData.get();
                                    const newOrder = [...currentData.sectionsOrder];
                                    const draggedIndex = newOrder.indexOf(draggedSectionKey);
                                    const targetIndex = newOrder.indexOf(targetSectionKey);

                                    if (draggedIndex !== -1 && targetIndex !== -1) {
                                        // Remove from current position
                                        newOrder.splice(draggedIndex, 1);
                                        // Insert at new position
                                        const insertIndex = insertBefore ? targetIndex : targetIndex + 1;
                                        newOrder.splice(insertIndex > draggedIndex ? insertIndex - 1 : insertIndex, 0, draggedSectionKey);

                                        m.reorderSections(newOrder);
                                    }
                                });
                            }
                        }
                    });
                });
            }
            const downloadBtn = document.getElementById('download-pdf-btn') as HTMLButtonElement;
            const clearBtn = document.getElementById('clear-data-btn');
            const sampleBtn = document.getElementById('sample-data-btn');
            if (!previewContent || !downloadBtn || !clearBtn || !sampleBtn) return;
            if (!previewWrapper && previewContent) {
                previewWrapper = (previewContent as HTMLElement).parentElement as HTMLElement | null;
            }

            let isInlineEditing = false; // prevent full rerender during keystrokes
            let isUploading = false;     // track resume upload-in-progress for spinner overlay
            const uploadOverlay = document.getElementById('upload-overlay') as HTMLElement | null;
            const showUploadOverlay = () => {
              if (!uploadOverlay) return;
              uploadOverlay.classList.remove('hidden');
              document.body.style.pointerEvents = 'none';
              document.body.style.cursor = 'progress';
            };
            const hideUploadOverlay = () => {
              if (!uploadOverlay) return;
              uploadOverlay.classList.add('hidden');
              document.body.style.pointerEvents = '';
              document.body.style.cursor = '';
            };

            // Palette: wire interactions once
            const palette = document.getElementById('sections-palette') as HTMLElement | null;
            const moreToggle = document.getElementById('more-sections-toggle') as HTMLButtonElement | null;
            const moreList = document.getElementById('more-sections') as HTMLElement | null;

            // Helper: smooth scroll to a section container
            function scrollToSection(sectionKeyOrTitle: string, kind: 'default' | 'custom') {
              const map: Record<string, string> = {
                summary: '#section-summary',
                workExperience: '#section-experience',
                education: '#section-education',
                skills: '#section-skills',
              };
              let target: HTMLElement | null = null;
              if (kind === 'default') {
                const sel = map[sectionKeyOrTitle] || '';
                target = sel ? document.querySelector(sel) as HTMLElement | null : null;
              } else {
                // Find custom section by title if possible, else last custom
                const sections = Array.from(document.querySelectorAll<HTMLElement>('#resume-preview-content [data-section-type="custom"]'));
                target = sections.find(s => (s.querySelector('[data-custom-field="title"]')?.textContent || '').trim().toLowerCase() === sectionKeyOrTitle.trim().toLowerCase()) || sections[sections.length - 1] || null;
              }
              if (target) {
                target.scrollIntoView({ behavior: 'smooth', block: 'center' });
              }
            }

            function updatePaletteState(dataState: any) {
              // Default rows (Summary, Experience, Education, Skills)
              document.querySelectorAll<HTMLElement>('[data-section-row]').forEach(row => {
                const key = row.getAttribute('data-section-row') as 'summary'|'workExperience'|'education'|'skills';
                const main = row.querySelector<HTMLButtonElement>('[data-section-chip]');
                const act = row.querySelector<HTMLButtonElement>('.chip-act');
                const isOn = !!dataState.sectionVisibility?.[key];
                if (main) {
                  main.classList.toggle('is-active', isOn);
                  main.setAttribute('aria-pressed', String(isOn));
                }
                if (act) {
                  if (isOn) {
                    act.textContent = '−';
                    act.dataset.mode = 'remove';
                    act.setAttribute('aria-label', `Remove ${key === 'workExperience' ? 'Experience' : key.charAt(0).toUpperCase() + key.slice(1)}`);
                  } else {
                    act.textContent = '+';
                    delete act.dataset.mode;
                    act.setAttribute('aria-label', `Add ${key === 'workExperience' ? 'Experience' : key.charAt(0).toUpperCase() + key.slice(1)}`);
                  }
                }
              });

              // Build list of visible custom section titles (unique)
              const visibleCustomTitles = Array.isArray(dataState.customSections)
                ? Array.from(new Set(dataState.customSections.filter((s:any) => s.visible !== false).map((s:any) => s.title)))
                : [];

              // Render visible custom list under "Sections"
              const visibleContainer = document.getElementById('sections-custom-visible');
              if (visibleContainer) {
                if (visibleCustomTitles.length === 0) {
                  visibleContainer.classList.add('hidden');
                  visibleContainer.innerHTML = '';
                } else {
                  visibleContainer.classList.remove('hidden');
                  visibleContainer.innerHTML = visibleCustomTitles.map(title => `
                    <div class="section-row" data-visible-custom="${title}">
                      <button class="section-chip chip-main is-active" data-visible-title="${title}" aria-label="Scroll to ${title}">${title}</button>
                      <button class="chip-act" data-action="remove-visible-custom" data-title="${title}" data-mode="remove" aria-label="Remove ${title}">−</button>
                    </div>
                  `).join('');
                }
              }

              // Dropdown "Add Section" rows: hide those already visible
              document.querySelectorAll<HTMLElement>('[data-more-row]').forEach(row => {
                const title = row.getAttribute('data-more-row')!;
                const isOn = visibleCustomTitles.includes(title);
                const main = row.querySelector<HTMLButtonElement>('[data-more-item]');
                const act = row.querySelector<HTMLButtonElement>('.chip-act');

                // Hide from dropdown if visible
                row.classList.toggle('hidden', isOn);

                if (main) {
                  main.classList.toggle('is-active', isOn);
                  main.setAttribute('aria-pressed', String(isOn));
                }
                if (act) {
                  if (isOn) {
                    act.textContent = '−';
                    act.dataset.mode = 'remove';
                    act.setAttribute('aria-label', `Remove ${title}`);
                  } else {
                    act.textContent = '+';
                    delete act.dataset.mode;
                    act.setAttribute('aria-label', `Add ${title}`);
                  }
                }
              });
            }

            if (palette && !(palette as any)._wired) {
              // Upload trigger from palette -> forwards to main upload control
              const paletteUpload = document.getElementById('sections-upload-btn');
              paletteUpload?.addEventListener('click', () => {
                // Try the builder's upload button first
                const mainUpload = document.getElementById('upload-resume-btn') as HTMLButtonElement | null;
                if (mainUpload) {
                  mainUpload.click();
                  return;
                }
                // Fallback directly to file input if present
                const fileInput = document.getElementById('resume-file-input') as HTMLInputElement | null;
                if (fileInput) fileInput.click();
              });

              // Hook into actual file input to show spinner during upload/parse
              const fileInput = document.getElementById('resume-file-input') as HTMLInputElement | null;
              if (fileInput && !(fileInput as any)._spinnerWired) {
                fileInput.addEventListener('change', () => {
                  if (fileInput.files && fileInput.files.length > 0) {
                    isUploading = true;
                    showUploadOverlay();
                  }
                });
                (fileInput as any)._spinnerWired = true;
              }

              // Also support custom events from other upload flows
              window.addEventListener('resume-upload-start', () => {
                isUploading = true;
                showUploadOverlay();
              });
              window.addEventListener('resume-upload-end', () => {
                isUploading = false;
                hideUploadOverlay();
              });
              // Toggle expand/collapse of More
              moreToggle?.addEventListener('click', () => {
                moreList?.classList.toggle('hidden');
              });

                // Toggle inline Customize panel within Sections
                const palCustToggle = document.getElementById('palette-customize-toggle') as HTMLButtonElement | null;
                const palCust = document.getElementById('palette-customize') as HTMLElement | null;
                palCustToggle?.addEventListener('click', () => {
                  palCust?.classList.toggle('hidden');
                  setTimeout(() => palette?.scrollIntoView({ behavior: 'smooth', block: 'nearest' }), 0);
                });

                // Wire inline palette customizer controls
                function wirePaletteCustomizer() {
                  const els = {
                    layout: document.getElementById('pc-layout') as HTMLSelectElement | null,
                    margins: document.getElementById('pc-margins') as HTMLSelectElement | null,
                    sectionSpacing: document.getElementById('pc-section-spacing') as HTMLSelectElement | null,
                    font: document.getElementById('pc-font') as HTMLSelectElement | null,
                    fontSize: document.getElementById('pc-font-size') as HTMLSelectElement | null,
                    line: document.getElementById('pc-line') as HTMLSelectElement | null,
                    date: document.getElementById('pc-date') as HTMLSelectElement | null,
                    hUnder: document.getElementById('pc-heading-underlined') as HTMLInputElement | null,
                    hCaps: document.getElementById('pc-heading-allcaps') as HTMLInputElement | null,
                    hRule: document.getElementById('pc-heading-rule') as HTMLInputElement | null,
                    hColors: document.getElementById('pc-heading-colors') as HTMLElement | null,
                    bColors: document.getElementById('pc-body-colors') as HTMLElement | null,
                    vSummary: document.getElementById('pc-vis-summary') as HTMLInputElement | null,
                    vWork: document.getElementById('pc-vis-work') as HTMLInputElement | null,
                    vEdu: document.getElementById('pc-vis-edu') as HTMLInputElement | null,
                    vSkills: document.getElementById('pc-vis-skills') as HTMLInputElement | null,
                    reset: document.getElementById('pc-reset') as HTMLButtonElement | null,
                  };

                  const deb = (fn: (...a:any[])=>void, ms=200)=>{ let t:any=null; return (...a:any[])=>{ if(t)clearTimeout(t); t=setTimeout(()=>fn(...a),ms);} };

                  const initFromState = (s:any) => {
                    if (els.layout) els.layout.value = s.layout;
                    if (els.margins) els.margins.value = s.margins;
                    if (els.sectionSpacing) els.sectionSpacing.value = s.sectionSpacing;
                    if (els.font) els.font.value = s.fontFamily;
                    if (els.fontSize) els.fontSize.value = s.fontSize;
                    if (els.line) els.line.value = s.lineSpacing;
                    if (els.date) els.date.value = s.dateFormat;
                    if (els.hUnder) els.hUnder.checked = !!s.headingStyles?.underlined;
                    if (els.hCaps) els.hCaps.checked = !!s.headingStyles?.allCaps;
                    if (els.hRule) els.hRule.checked = !!s.headingStyles?.horizontalRule;
                    if (els.vSummary) els.vSummary.checked = !!s.sectionVisibility?.summary;
                    if (els.vWork) els.vWork.checked = !!s.sectionVisibility?.workExperience;
                    if (els.vEdu) els.vEdu.checked = !!s.sectionVisibility?.education;
                    if (els.vSkills) els.vSkills.checked = !!s.sectionVisibility?.skills;
                  };

                  initFromState(resumeData.get());

                  els.layout?.addEventListener('change', e => import('../../lib/resumeBuilderService').then(m => m.setLayout((e.target as HTMLSelectElement).value as any)));
                  els.margins?.addEventListener('change', e => import('../../lib/resumeBuilderService').then(m => m.setMargins((e.target as HTMLSelectElement).value)));
                  els.sectionSpacing?.addEventListener('change', e => import('../../lib/resumeBuilderService').then(m => m.setSectionSpacing((e.target as HTMLSelectElement).value)));
                  els.font?.addEventListener('change', deb(e => import('../../lib/resumeBuilderService').then(m => m.setFontFamily((e.target as HTMLSelectElement).value))));
                  els.fontSize?.addEventListener('change', deb(e => import('../../lib/resumeBuilderService').then(m => m.setFontSize((e.target as HTMLSelectElement).value))));
                  els.line?.addEventListener('change', deb(e => import('../../lib/resumeBuilderService').then(m => m.setLineSpacing((e.target as HTMLSelectElement).value))));
                  els.date?.addEventListener('change', e => import('../../lib/resumeBuilderService').then(m => m.setDateFormat((e.target as HTMLSelectElement).value)));

                  els.hUnder?.addEventListener('change', e => import('../../lib/resumeBuilderService').then(m => m.setHeadingStyle('underlined', (e.target as HTMLInputElement).checked)));
                  els.hCaps?.addEventListener('change', e => import('../../lib/resumeBuilderService').then(m => m.setHeadingStyle('allCaps', (e.target as HTMLInputElement).checked)));
                  els.hRule?.addEventListener('change', e => import('../../lib/resumeBuilderService').then(m => m.setHeadingStyle('horizontalRule', (e.target as HTMLInputElement).checked)));

                  const wireColorPicker = (el: HTMLElement | null, setter: (c:string|null)=>void) => {
                    if (!el) return;
                    el.addEventListener('click', e => {
                      const btn = (e.target as HTMLElement).closest('button[data-color]');
                      if (!btn) return;
                      const color = btn.getAttribute('data-color');
                      setter(color === 'default' ? null : (color || null));
                    });
                  };
                  wireColorPicker(els.hColors, (c)=> import('../../lib/resumeBuilderService').then(m => m.setHeadingColor(c)));
                  wireColorPicker(els.bColors, (c)=> import('../../lib/resumeBuilderService').then(m => m.setBodyTextColor(c ?? '#000000')));

                  els.vSummary?.addEventListener('change', e => import('../../lib/resumeBuilderService').then(m => m.setSectionVisibility('summary', (e.target as HTMLInputElement).checked)));
                  els.vWork?.addEventListener('change', e => import('../../lib/resumeBuilderService').then(m => m.setSectionVisibility('workExperience', (e.target as HTMLInputElement).checked)));
                  els.vEdu?.addEventListener('change', e => import('../../lib/resumeBuilderService').then(m => m.setSectionVisibility('education', (e.target as HTMLInputElement).checked)));
                  els.vSkills?.addEventListener('change', e => import('../../lib/resumeBuilderService').then(m => m.setSectionVisibility('skills', (e.target as HTMLInputElement).checked)));

                  els.reset?.addEventListener('click', () => import('../../lib/resumeBuilderService').then(m => m.resetStyles()));

                  // Keep inputs in sync if settings change elsewhere
                  resumeData.subscribe((s:any)=> initFromState(s));
                }
                wirePaletteCustomizer();

                // Initialize palette state once on load
                updatePaletteState(resumeData.get());

                // Accordion behavior for inline customizer: only one open at a time
                const accButtons = Array.from(document.querySelectorAll<HTMLButtonElement>('#palette-customize [data-pc-acc]'));
                const accPanels = Array.from(document.querySelectorAll<HTMLElement>('#palette-customize [data-pc-panel]'));
                const openPanel = (key: string) => {
                  accPanels.forEach(p => {
                    const isTarget = p.getAttribute('data-pc-panel') === key;
                    p.classList.toggle('hidden', !isTarget);
                  });
                };
                // Ensure "layout" starts open (markup already shows it; enforce state)
                openPanel('layout');
                accButtons.forEach(btn => {
                  btn.addEventListener('click', () => {
                    const key = btn.getAttribute('data-pc-acc') || '';
                    const open = accPanels.find(p => !p.classList.contains('hidden'))?.getAttribute('data-pc-panel');
                    if (open === key) {
                      // collapse all
                      accPanels.forEach(p => p.classList.add('hidden'));
                    } else {
                      openPanel(key);
                    }
                    // Keep the palette area in view after toggling
                    setTimeout(() => btn.scrollIntoView({ behavior: 'smooth', block: 'nearest' }), 0);
                  });
                });

               // Default section: main button scrolls, +/- toggles
               document.querySelectorAll<HTMLButtonElement>('#sections-default [data-section-chip]').forEach(btn => {
                 btn.addEventListener('click', () => {
                   const key = btn.dataset.sectionChip as 'summary'|'workExperience'|'education'|'skills';
                   const state = resumeData.get();
                   const isOn = !!state.sectionVisibility?.[key];
                   if (isOn) {
                     scrollToSection(key, 'default');
                   } else {
                     // Not present: do nothing on name click
                   }
                 });
               });
               document.querySelectorAll<HTMLButtonElement>('#sections-default .chip-act[data-action="toggle-default"]').forEach(btn => {
                 btn.addEventListener('click', (e) => {
                   e.stopPropagation();
                   const key = (btn.dataset.key as 'summary'|'workExperience'|'education'|'skills');
                   const state = resumeData.get();
                   const isOn = !!state.sectionVisibility?.[key];
                   if (isOn) {
                     setSectionVisibility(key, false);
                   } else {
                     setSectionVisibility(key, true);
                     setTimeout(() => scrollToSection(key, 'default'), 100);
                   }
                 });
               });

              // More menu items: main button scrolls if present, +/- toggles
              document.querySelectorAll<HTMLButtonElement>('#more-sections [data-more-item]').forEach(btn => {
                btn.addEventListener('click', () => {
                  const title = btn.dataset.moreItem!;
                  const state = resumeData.get();
                  const existsVisible = Array.isArray(state.customSections) && state.customSections.some((s:any)=> s.title === title && s.visible !== false);
                  if (existsVisible) {
                    scrollToSection(title, 'custom');
                  } else {
                    // Not present: do nothing on name click
                  }
                });
              });
              document.querySelectorAll<HTMLButtonElement>('#more-sections .chip-act[data-action="toggle-custom"]').forEach(btn => {
                btn.addEventListener('click', (e) => {
                  e.stopPropagation();
                  const title = btn.dataset.title!;
                  const state = resumeData.get();
                  const existsVisible = Array.isArray(state.customSections) && state.customSections.some((s:any)=> s.title === title && s.visible !== false);
                  if (existsVisible) {
                    removeCustomSectionsByTitle(title);
                  } else {
                    addCustomSectionWithTitle(title);
                    setTimeout(() => scrollToSection(title, 'custom'), 120);
                  }
                });
              });

              // Visible custom list interactions (scroll and remove)
              const customVisibleContainer = document.getElementById('sections-custom-visible');
              customVisibleContainer?.addEventListener('click', (e) => {
                const el = e.target as HTMLElement;
                const scrollBtn = el.closest('[data-visible-title]') as HTMLButtonElement | null;
                const removeBtn = el.closest('[data-action="remove-visible-custom"]') as HTMLButtonElement | null;
                if (scrollBtn) {
                  const title = scrollBtn.getAttribute('data-visible-title')!;
                  scrollToSection(title, 'custom');
                } else if (removeBtn) {
                  const title = removeBtn.getAttribute('data-title')!;
                  removeCustomSectionsByTitle(title);
                }
              });

              (palette as any)._wired = true;
            }
            let lastClickedBullet: HTMLElement | null = null; // Track last clicked bullet for deletion
            const renderPreview = (data) => {
                // Apply dynamic styles
                const styleTag = document.getElementById('resume-dynamic-styles') || document.createElement('style');
                styleTag.id = 'resume-dynamic-styles';
                styleTag.innerHTML = `
                    #resume-preview-content {
                        font-family: ${data.fontFamily};
                        font-size: ${data.fontSize};
                        line-height: ${data.lineSpacing};
                        color: ${data.bodyTextColor};
                    }
                    #resume-preview-content.harvard h2 {
                        letter-spacing: 0.02em;
                    }
                    #resume-preview-content h1 {
                        font-size: ${data.headingStyles.h1FontSize};
                        font-weight: ${data.headingStyles.h1FontWeight};
                    }
                    #resume-preview-content h2 {
                        font-size: ${data.headingStyles.h2FontSize};
                        font-weight: ${data.headingStyles.h2FontWeight};
                    }
                    #resume-preview-content h1,
                    #resume-preview-content h2,
                    #resume-preview-content h3 {
                        color: ${data.headingColor || data.bodyTextColor};
                        text-transform: ${data.headingStyles.allCaps ? 'uppercase' : 'none'};
                        text-decoration: ${data.headingStyles.underlined ? 'underline' : 'none'};
                        border-bottom: ${data.headingStyles.horizontalRule ? `1px solid ${data.bodyTextColor}` : 'none'};
                        padding-bottom: ${data.headingStyles.horizontalRule ? '0.25rem' : '0'};
                    }
                    #resume-preview-content .section-margin {
                       margin-bottom: ${data.sectionSpacing === 'compact' ? '0.5rem' : data.sectionSpacing === 'relaxed' ? '1.5rem' : data.sectionSpacing === 'extra-relaxed' ? '2rem' : '1rem'};
                    }
                    #resume-preview-content ul {
                        list-style-position: outside; /* bullets outside so user credentials line doesn't show bullets */
                        padding-left: 1.25rem;
                        margin: 0.25rem 0 0.25rem 0;
                    }
                    #resume-preview-content .heading-bar {
                        text-transform: uppercase;
                        font-weight: 700;
                        border-bottom: 1px solid ${data.bodyTextColor};
                        padding-bottom: 0.15rem;
                        margin-bottom: 0.4rem;
                        letter-spacing: 0.03em;
                    }
                    #resume-preview-content .meta-line {
                        display: flex; justify-content: space-between; align-items: baseline;
                    }
                    #resume-preview-content .company-role { font-weight: 700; }
                    #resume-preview-content .italic { font-style: italic; }

                     /* Interactive affordances (unscoped so they apply to injected HTML) */
                    #resume-preview-content .item-selected {
                        outline: 2px solid #10b981;
                        outline-offset: 2px;
                        border-radius: 0.5rem;
                        background: rgba(16,185,129,0.04);
                        transition: outline-color 0.2s ease, background-color 0.2s ease;
                    }
                    .dark #resume-preview-content .item-selected {
                        outline-color: #34d399;
                        background: rgba(16,185,129,0.08);
                    }

                    /* Entry hover (Experience/Education and section containers) */
                    #resume-preview-content .item-container[data-work-id],
                    #resume-preview-content .item-container[data-edu-id],
                    #resume-preview-content .item-container[data-section-type] {
                        border-radius: 0.5rem;
                        transition: outline-color 0.18s ease, box-shadow 0.18s ease, background-color 0.18s ease, transform 0.18s ease;
                    }
                    #resume-preview-content .item-container[data-work-id]:hover:not(.item-selected),
                    #resume-preview-content .item-container[data-edu-id]:hover:not(.item-selected),
                    #resume-preview-content .item-container[data-section-type]:hover:not(.item-selected) {
                        outline: 2px solid rgba(99,102,241,0.28);
                        outline-offset: 2px;
                        background: rgba(99,102,241,0.04);
                    }
                    .dark #resume-preview-content .item-container[data-work-id]:hover:not(.item-selected),
                    .dark #resume-preview-content .item-container[data-edu-id]:hover:not(.item-selected),
                    .dark #resume-preview-content .item-container[data-section-type]:hover:not(.item-selected) {
                        outline-color: rgba(129,140,248,0.38);
                        background: rgba(99,102,241,0.08);
                    }

                    /* Bullet hover */
                    #resume-preview-content li[contenteditable="true"][data-bullet-id] {
                        border-radius: 0.25rem;
                        transition: outline-color 0.18s ease, background-color 0.18s ease;
                    }
                    #resume-preview-content li[contenteditable="true"][data-bullet-id]:hover:not(:focus) {
                        outline: 1.5px dashed rgba(99,102,241,0.35);
                        outline-offset: 3px;
                        background: rgba(99,102,241,0.05);
                    }
                    .dark #resume-preview-content li[contenteditable="true"][data-bullet-id]:hover:not(:focus) {
                        outline-color: rgba(129,140,248,0.45);
                        background: rgba(99,102,241,0.09);
                    }

                    /* Suppress stacked outlines when an entry is selected or when a child is hovered */
                    #resume-preview-content .item-container.item-selected li[contenteditable="true"][data-bullet-id]:hover {
                        outline: none;
                        background: transparent;
                    }
                    #resume-preview-content .item-container[data-section-type]:has(.item-container:hover),
                    #resume-preview-content .item-container[data-section-type]:has(.item-container.item-selected) {
                        outline: none !important;
                        background: transparent !important;
                    }
                `;
                document.head.appendChild(styleTag);

                previewContent.style.padding = data.margins === 'narrow' ? '0.5rem 0.75rem' : data.margins === 'wide' ? '1.5rem 2rem' : '1rem 1.5rem';
                previewContent.classList.remove('two-column');

                // Updated formatDate function with proper range handling
                const formatDate = (dateStr, format = data.dateFormat) => {
                    if (!dateStr) return '';
                    const [year, month] = dateStr.split('-');
                    const date = new Date(parseInt(year), parseInt(month) - 1);

                    switch(format) {
                        case 'short':
                            return date.toLocaleDateString('en-US', { year: 'numeric', month: 'short' });
                        case 'long':
                            return date.toLocaleDateString('en-US', { year: 'numeric', month: 'long' });
                        case 'year-only':
                            return date.toLocaleDateString('en-US', { year: 'numeric' });
                        case 'full-date':
                            return date.toLocaleDateString('en-US', { month: '2-digit', year: 'numeric' }).replace(/\//g, '/');
                        case 'range':
                            return date.toLocaleDateString('en-US', { year: 'numeric' });
                        default:
                            return date.toLocaleDateString('en-US', { year: 'numeric', month: 'short' });
                    }
                };

                // New formatRange function to handle date ranges properly
                const formatRange = (startDateStr, endDateStr, format = data.dateFormat) => {
                    if (!startDateStr) return '';

                    // If no end date, show "Present"
                    if (endDateStr === 'Present') {
                        return `${formatDate(startDateStr, format)} - Present`;
                    }

                    // If only year format, show year range
                    if (format === 'year-only') {
                        const startYear = startDateStr.split('-')[0];
                        const endYear = endDateStr ? endDateStr.split('-')[0] : 'Present';
                        return `${startYear} - ${endYear}`;
                    }

                    // For range format, show years only
                    if (format === 'range') {
                        const startYear = startDateStr.split('-')[0];
                        const endYear = endDateStr ? endDateStr.split('-')[0] : 'Present';
                        return `${startYear} - ${endYear}`;
                    }

                    // For other formats, show full date range
                    const startDateFormatted = formatDate(startDateStr, format);
                    const endDateFormatted = endDateStr ? formatDate(endDateStr, format) : 'Present';
                    return `${startDateFormatted} - ${endDateFormatted}`;
                };
                // Harvard layout builder (single column)
                    const contactSegments: string[] = [];
                    if (data.contactInfo.email) contactSegments.push(`<span contenteditable="true" data-field="email">${data.contactInfo.email}</span>`);
                    if (data.contactInfo.phone) contactSegments.push(`<span contenteditable="true" data-field="phone">${data.contactInfo.phone}</span>`);
                    if (data.contactInfo.location) contactSegments.push(`<span contenteditable="true" data-field="location">${data.contactInfo.location}</span>`);
                    if (data.contactInfo.linkedin) contactSegments.push(`<span contenteditable="true" data-field="linkedin">${data.contactInfo.linkedin}</span>`);
                    if (data.contactInfo.portfolio) contactSegments.push(`<span contenteditable="true" data-field="portfolio">${data.contactInfo.portfolio}</span>`);

                const harvard = [] as string[];

                // Always render header first
                harvard.push(`
                    <header class="item-container" style="text-align:center; margin-bottom:0.5rem; position:relative;" data-section-type="header">
                        <h1 style="margin:0; border:none;" contenteditable="true" data-field="fullName">${data.contactInfo.fullName || 'Your Name'}</h1>
                        <div class="italic" style="border:none; padding:0; margin-top:0.25rem;">
                            ${data.contactInfo.role ? `<div contenteditable="true" data-field="role" style="margin-bottom:0.25rem;">${data.contactInfo.role}</div>` : ''}
                            <div class="contact-line">
                                ${contactSegments.join(' <span class="sep mx-1 text-gray-400">|</span> ')}
                            </div>
                        </div>
                    </header>
                `);

                // Function to render each section
                const renderSection = (sectionName: string) => {
                    switch (sectionName) {
                        case 'summary':
                            if (data.sectionVisibility.summary) {
                                harvard.push(`
                                    <section class="section-margin item-container" id="section-summary" data-section-type="summary" draggable="true">
                                        <div class="heading-bar">Summary</div>
                                        <div id="inline-summary" contenteditable="true" data-placeholder="Write a short professional summary..." style="outline:none;">${data.summary || ''}</div>
                                    </section>
                                `);
                            }
                            break;

                        case 'workExperience':
                            if (data.sectionVisibility.workExperience) {
                                harvard.push(`<section class="section-margin item-container" id="section-experience" data-section-type="work" draggable="true"><div class="heading-bar">Experience</div>`);
                                if (Array.isArray(data.workExperience) && data.workExperience.length) {
                                    data.workExperience.forEach((job, index) => {
                                        harvard.push(`
                                            <div class="mt-3 item-container reordering-item" data-work-id="${job.id}" data-index="${index}">
                                                <div class="meta-line">
                                                    <div class="company-role">
                                                        <span contenteditable="true" data-work-field="jobTitle" data-work-id="${job.id}">${job.jobTitle || 'Job Title'}</span>${job.company ? `, <span contenteditable="true" data-work-field="company" data-work-id="${job.id}">${job.company}</span>` : ', <span contenteditable="true" data-work-field="company" data-work-id="${job.id}" data-placeholder="Company Name">Company Name</span>'}
                                                    </div>
                                                    <div class="italic">
                                                        ${job.location ? `<span contenteditable="true" data-work-field="location" data-work-id="${job.id}">${job.location}</span> \u00B7 ` : '<span contenteditable="true" data-work-field="location" data-work-id="${job.id}" data-placeholder="Location">Location</span> \u00B7 '}
                                                        <span contenteditable="true" data-work-field="startDate" data-work-id="${job.id}">${formatRange(job.startDate, job.endDate) || 'Start Date - End Date'}</span>
                                                    </div>
                                                </div>
                                                <ul class="list-disc mt-1" data-bullets>
                                                    ${(job.bulletPoints && job.bulletPoints.length > 0
                                                        ? job.bulletPoints.map(bp => `
                                                          <li contenteditable="true" data-bullet-id="${bp.id}">
                                                            ${bp.text || ''}
                                                          </li>`).join('')
                                                        : '<li contenteditable="true">Describe impact and achievements</li>')}
                                                </ul>
                                            </div>
                                        `);
                                    });
                                } else {
                                    harvard.push(`<p class="text-gray-500 italic mt-1">Add your work experience.</p>`);
                                }
                                harvard.push(`<button class="text-xs text-indigo-600 mt-2" data-action="add-job">+ Add Job</button></section>`);
                            }
                            break;

                        case 'education':
                            if (data.sectionVisibility.education) {
                                harvard.push(`<section class="section-margin item-container" id="section-education" data-section-type="edu" draggable="true"><div class="heading-bar">Education</div>`);
                                if (Array.isArray(data.education) && data.education.length) {
                                    data.education.forEach((edu, index) => {
                                        harvard.push(`
                                            <div class="mt-3 item-container reordering-item" data-edu-id="${edu.id}" data-index="${index}">
                                                <div class="meta-line">
                                                    <div class="company-role">
                                                        <span contenteditable="true" data-edu-field="degree" data-edu-id="${edu.id}">${edu.degree || 'Degree'}</span>${edu.fieldOfStudy ? `, <span contenteditable="true" data-edu-field="fieldOfStudy" data-edu-id="${edu.id}">${edu.fieldOfStudy}</span>` : ', <span contenteditable="true" data-edu-field="fieldOfStudy" data-edu-id="${edu.id}" data-placeholder="Field of Study">Field of Study</span>'} —
                                                        <span contenteditable="true" data-edu-field="institution" data-edu-id="${edu.id}">${edu.institution || 'Institution'}</span>
                                                    </div>
                                                    <div class="italic">
                                                        <span contenteditable="true" data-edu-field="graduationDate" data-edu-id="${edu.id}">${formatDate(edu.graduationDate) || 'Graduation Date'}</span>
                                                    </div>
                                                </div>
                                                <ul class="list-disc mt-1" data-bullets>
                                                    ${(edu.bulletPoints && edu.bulletPoints.length > 0
                                                        ? edu.bulletPoints.map(bp => `
                                                          <li contenteditable="true" data-bullet-id="${bp.id}">
                                                            ${bp.text || ''}
                                                          </li>`).join('')
                                                        : '<li contenteditable="true">Relevant coursework, honors, activities, or achievements</li>')}
                                                </ul>
                                            </div>
                                        `);
                                    });
                                } else {
                                    harvard.push(`<p class="text-gray-500 italic mt-1">Add your education history.</p>`);
                                }
                                harvard.push(`<button class="text-xs text-indigo-600 mt-2" data-action="add-edu">+ Add Education</button></section>`);
                            }
                            break;

                        case 'skills':
                            if (data.sectionVisibility.skills) {
                                // Use stored raw text if available, otherwise reconstruct from data
                                let skillsContent = '';

                                // Check if data has changed (new upload) - compare with stored data hash
                                const currentDataHash = JSON.stringify(data.skills);
                                const storedDataHash = (window as any).lastSkillsDataHash;
                                const hasDataChanged = currentDataHash !== storedDataHash;

                                // If data changed or no cached text, regenerate from data
                                if (hasDataChanged || !(window as any).lastSkillsText || isInlineEditing) {
                                    if (data.skills && data.skills.length > 0) {
                                        // Check if skills are in categorized format
                                        if (typeof data.skills[0] === 'object' && data.skills[0].category) {
                                            // Categorized skills format - render as plain text for easy editing
                                            skillsContent = data.skills.map(cat =>
                                                `${cat.category}: ${cat.items.join(', ')}`
                                            ).join('\n');
                                        } else {
                                            // Flat array format
                                            skillsContent = (data.skills as string[]).join('\n');
                                        }
                                        // Store this as the current text and data hash
                                        (window as any).lastSkillsText = skillsContent;
                                        (window as any).lastSkillsDataHash = currentDataHash;
                                    }
                                } else {
                                    // Use cached text from previous edits
                                    skillsContent = (window as any).lastSkillsText;
                                }

                                harvard.push(`
                                    <section class="section-margin item-container" id="section-skills" data-section-type="skills" draggable="true">
                                        <div class="heading-bar">Skills</div>
                                        <div id="inline-skills" contenteditable="true" data-placeholder="e.g., Technical Skills: JavaScript, React, Node.js&#10;Soft Skills: Communication, Leadership" style="white-space: pre-wrap; min-height: 60px; line-height: 1.4;">${skillsContent}</div>
                                    </section>
                                `);
                            }
                            break;

                        case 'customSections':
                            const customVisible = data.customSections.filter(s => s.visible);
                            customVisible.forEach((section) => {
                                harvard.push(`
                                    <section class="section-margin item-container" data-custom-id="${section.id}" data-section-type="custom" draggable="true">
                                        <div class="heading-bar" contenteditable="true" data-custom-field="title">${section.title}</div>
                                        <ul class="list-disc mt-1" data-bullets>
                                            ${(section.bulletPoints && section.bulletPoints.length > 0
                                                ? section.bulletPoints.map(bp => `
                                                  <li contenteditable="true" data-bullet-id="${bp.id}">
                                                    ${bp.text || ''}
                                                  </li>`).join('')
                                                : '<li contenteditable="true">Add content for this section</li>')}
                                        </ul>
                                    </section>
                                `);
                            });
                            break;
                    }
                };

                // Render sections in the order specified by sectionsOrder
                const sectionsOrder = data.sectionsOrder || ['summary', 'workExperience', 'education', 'skills', 'customSections'];
                sectionsOrder.forEach(sectionName => {
                    renderSection(sectionName);
                });

                // Add custom section button at the end
                harvard.push(`<div class="item-container" data-section-type="custom"><button class="text-xs text-indigo-600 mt-2" data-action="add-custom">+ Add Custom Section</button></div>`);

                previewContent.classList.add('harvard');
                previewContent.innerHTML = harvard.join('');

                // Inline editing wiring
                const debounce = (fn, ms = 200) => { let t; return (...a) => { clearTimeout(t); t = setTimeout(() => fn(...a), ms); }; };
                    const inlineSummary = document.getElementById('inline-summary');
                    if (inlineSummary) {
                        // Prevent focus loss while typing by avoiding full rerender on every keystroke
                        inlineSummary.addEventListener('focus', () => { isInlineEditing = true; });
                        inlineSummary.addEventListener('blur', () => { isInlineEditing = false; if (latest) requestAnimationFrame(() => renderPreview(latest)); });
                        inlineSummary.addEventListener('input', debounce(() => {
                            import('../../lib/resumeBuilderService').then(m => m.updateSection('summary', (inlineSummary as HTMLElement).innerText.trim()));
                        }));
                    }
                const inlineSkills = document.getElementById('inline-skills');
                if (inlineSkills) {
                    let lastSkillsContent = '';

                    // Prevent focus loss while typing by avoiding full rerender on every keystroke
                    inlineSkills.addEventListener('focus', () => {
                        isInlineEditing = true;
                        lastSkillsContent = (inlineSkills as HTMLElement).innerText;
                    });

                    inlineSkills.addEventListener('blur', () => {
                        isInlineEditing = false;
                        // Store the current content before re-render
                        lastSkillsContent = (inlineSkills as HTMLElement).innerText;

                        // Don't trigger full re-render, just update the data
                        const content = lastSkillsContent.trim();

                        // Parse and save the skills data
                        const lines = content.split('\n').map(line => line.trim()).filter(line => line);
                        const skills: { category: string; items: string[] }[] = [];
                        let flatSkills: string[] = [];

                        for (const line of lines) {
                            if (line.includes(':')) {
                                const colonIndex = line.indexOf(':');
                                const category = line.substring(0, colonIndex).trim();
                                const itemsStr = line.substring(colonIndex + 1).trim();

                                if (category && itemsStr) {
                                    const items = itemsStr.split(/[,;]/).map(skill => skill.trim()).filter(skill => skill);
                                    if (items.length > 0) {
                                        skills.push({ category, items });
                                    }
                                }
                            } else if (line.length > 0) {
                                const lineSkills = line.split(/[,;]/).map(skill => skill.trim()).filter(skill => skill);
                                flatSkills.push(...lineSkills);
                            }
                        }

                        let skillsToSave: string[] | { category: string; items: string[] }[];
                        if (skills.length > 0) {
                            if (flatSkills.length > 0) {
                                skills.push({ category: 'Other Skills', items: flatSkills });
                            }
                            skillsToSave = skills;
                        } else {
                            skillsToSave = flatSkills;
                        }

                        // Save the data but don't trigger re-render
                        import('../../lib/resumeBuilderService').then(m => {
                            m.updateSection('skills', skillsToSave as any);
                            // Store the raw text content for future renders
                            (window as any).lastSkillsText = content;
                        });
                    });

                    inlineSkills.addEventListener('input', debounce(() => {
                        const content = (inlineSkills as HTMLElement).innerText.trim();
                        lastSkillsContent = content;

                        // Parse the content for live updates
                        const lines = content.split('\n').map(line => line.trim()).filter(line => line);
                        const skills: { category: string; items: string[] }[] = [];
                        let flatSkills: string[] = [];

                        for (const line of lines) {
                            if (line.includes(':')) {
                                const colonIndex = line.indexOf(':');
                                const category = line.substring(0, colonIndex).trim();
                                const itemsStr = line.substring(colonIndex + 1).trim();

                                if (category && itemsStr) {
                                    const items = itemsStr.split(/[,;]/).map(skill => skill.trim()).filter(skill => skill);
                                    if (items.length > 0) {
                                        skills.push({ category, items });
                                    }
                                }
                            } else if (line.length > 0) {
                                const lineSkills = line.split(/[,;]/).map(skill => skill.trim()).filter(skill => skill);
                                flatSkills.push(...lineSkills);
                            }
                        }

                        let skillsToSave: string[] | { category: string; items: string[] }[];
                        if (skills.length > 0) {
                            if (flatSkills.length > 0) {
                                skills.push({ category: 'Other Skills', items: flatSkills });
                            }
                            skillsToSave = skills;
                        } else {
                            skillsToSave = flatSkills;
                        }

                        import('../../lib/resumeBuilderService').then(m => {
                            m.updateSection('skills', skillsToSave as any);
                            // Store the raw text content
                            (window as any).lastSkillsText = content;
                        });
                    }));

                    // Handle Enter key to create new lines properly
                    inlineSkills.addEventListener('keydown', (e) => {
                        if (e.key === 'Enter') {
                            e.preventDefault();
                            const selection = window.getSelection();
                            const range = selection?.getRangeAt(0);
                            if (range) {
                                const br = document.createElement('br');
                                range.deleteContents();
                                range.insertNode(br);
                                range.setStartAfter(br);
                                range.setEndAfter(br);
                                selection?.removeAllRanges();
                                selection?.addRange(range);
                            }
                        }
                    });
                }

                // Contact info inline editing
                previewContent.querySelectorAll('[data-field]').forEach(field => {
                    const fieldName = (field as HTMLElement).dataset.field!;
                    field.addEventListener('focus', () => { isInlineEditing = true; });
                    field.addEventListener('blur', () => { isInlineEditing = false; if (latest) requestAnimationFrame(() => renderPreview(latest)); });
                    field.addEventListener('input', debounce(() => {
                        import('../../lib/resumeBuilderService').then(m => {
                            const curr = m.resumeData.get();
                            const updated = {
                                ...curr,
                                contactInfo: {
                                    ...curr.contactInfo,
                                    [fieldName]: (field as HTMLElement).innerText.trim()
                                }
                            };
                            m.setResumeData(updated);
                        });
                    }));
                });

                // Work experience fields inline editing
                previewContent.querySelectorAll('[data-work-field]').forEach(field => {
                    const fieldName = (field as HTMLElement).dataset.workField!;
                    const workId = (field as HTMLElement).dataset.workId!;
                    field.addEventListener('focus', () => { isInlineEditing = true; });
                    field.addEventListener('blur', () => { isInlineEditing = false; if (latest) requestAnimationFrame(() => renderPreview(latest)); });
                    field.addEventListener('input', debounce(() => {
                        import('../../lib/resumeBuilderService').then(m => {
                            const curr = m.resumeData.get();
                            const list = curr.workExperience.map(j => j.id === workId ? {
                                ...j,
                                [fieldName]: (field as HTMLElement).innerText.trim()
                            } : j);
                            m.updateSection('workExperience', list as any);
                        });
                    }));
                });

                // Education fields inline editing
                previewContent.querySelectorAll('[data-edu-field]').forEach(field => {
                    const fieldName = (field as HTMLElement).dataset.eduField!;
                    const eduId = (field as HTMLElement).dataset.eduId!;
                    field.addEventListener('focus', () => { isInlineEditing = true; });
                    field.addEventListener('blur', () => { isInlineEditing = false; if (latest) requestAnimationFrame(() => renderPreview(latest)); });
                    field.addEventListener('input', debounce(() => {
                        import('../../lib/resumeBuilderService').then(m => {
                            const curr = m.resumeData.get();
                            const list = curr.education.map(e => e.id === eduId ? {
                                ...e,
                                [fieldName]: (field as HTMLElement).innerText.trim()
                            } : e);
                            m.updateSection('education', list as any);
                        });
                    }));
                });

                // Education bullets inline
                previewContent.querySelectorAll('[data-edu-id]').forEach((eduEl) => {
                    const eduId = (eduEl as HTMLElement).dataset.eduId!;
                    eduEl.querySelectorAll('li[contenteditable][data-bullet-id]').forEach(li => {
                        li.addEventListener('focus', () => { isInlineEditing = true; });
                        li.addEventListener('blur', () => { isInlineEditing = false; if (latest) requestAnimationFrame(() => renderPreview(latest)); });
                        li.addEventListener('click', () => {
                            lastClickedBullet = li as HTMLElement;
                            console.log('Education bullet clicked, stored for deletion:', lastClickedBullet.dataset.bulletId);
                        });
                        li.addEventListener('input', debounce(() => {
                            const text = (li as HTMLElement).innerText.trim().replace(/\u00A0/g, '');
                            console.log('Education bullet input changed:', `"${text}"`, 'Length:', text.length);

                            import('../../lib/resumeBuilderService').then(m => {
                                const curr = m.resumeData.get();

                                // If the bullet is now empty and there are multiple bullets, delete it
                                if (text === '' || text.length === 0) {
                                    const currentEdu = curr.education.find(e => e.id === eduId);
                                    if (currentEdu && currentEdu.bulletPoints && currentEdu.bulletPoints.length > 1) {
                                        console.log('Auto-deleting empty education bullet');
                                        const list = curr.education.map(e => e.id === eduId ? {
                                            ...e,
                                            bulletPoints: (e.bulletPoints || []).filter(bp => bp.id !== (li as HTMLElement).dataset.bulletId)
                                        } : e);
                                        m.updateSection('education', list as any);
                                        return;
                                    }
                                }

                                // Update the bullet text
                                const list = curr.education.map(e => e.id === eduId ? {
                                    ...e,
                                    bulletPoints: (e.bulletPoints || []).map(bp =>
                                        bp.id === (li as HTMLElement).dataset.bulletId
                                            ? { ...bp, text: text }
                                            : bp)
                                } : e);
                                m.updateSection('education', list as any);
                            });
                        }));
                        li.addEventListener('keydown', (e) => {
                            const key = (e as KeyboardEvent).key;
                            const text = (li as HTMLElement).innerText.trim().replace(/\u00A0/g, '');
                            console.log('Keydown on education bullet:', key, 'Text:', `"${text}"`, 'Length:', text.length);

                            // Handle Enter key - create new bullet point
                            if (key === 'Enter') {
                                e.preventDefault();
                                console.log('Creating new education bullet point');
                                import('../../lib/resumeBuilderService').then(m => {
                                    const curr = m.resumeData.get();
                                    const currentEdu = curr.education.find(e => e.id === eduId);
                                    if (currentEdu && currentEdu.bulletPoints) {
                                        const currentBulletIndex = currentEdu.bulletPoints.findIndex(bp => bp.id === (li as HTMLElement).dataset.bulletId);
                                        const newBullet = { id: crypto.randomUUID(), text: '' };
                                        const newBullets = [...currentEdu.bulletPoints];
                                        newBullets.splice(currentBulletIndex + 1, 0, newBullet);

                                        const list = curr.education.map(e => e.id === eduId ? {
                                            ...e,
                                            bulletPoints: newBullets
                                        } : e);
                                        m.updateSection('education', list as any);

                                        // Focus the new bullet after a brief delay
                                        setTimeout(() => {
                                            const newBulletElement = eduEl.querySelector(`li[data-bullet-id="${newBullet.id}"]`) as HTMLElement;
                                            if (newBulletElement) {
                                                console.log('Focusing new education bullet element');
                                                newBulletElement.focus();
                                            }
                                        }, 100);
                                    }
                                });
                            }

                            // Handle Backspace on empty bullet - delete if multiple bullets exist
                            else if (key === 'Backspace' && (text === '' || text.length === 0)) {
                                console.log('Backspace on empty education bullet, checking if we can delete');
                                import('../../lib/resumeBuilderService').then(m => {
                                    const curr = m.resumeData.get();
                                    const currentEdu = curr.education.find(e => e.id === eduId);
                                    if (currentEdu && currentEdu.bulletPoints && currentEdu.bulletPoints.length > 1) {
                                        e.preventDefault();
                                        console.log('Deleting empty education bullet via backspace');
                                        const list = curr.education.map(e => e.id === eduId ? {
                                            ...e,
                                            bulletPoints: (e.bulletPoints || []).filter(bp => bp.id !== (li as HTMLElement).dataset.bulletId)
                                        } : e);
                                        m.updateSection('education', list as any);
                                    } else {
                                        console.log('Cannot delete - only one education bullet remaining');
                                    }
                                });
                            }
                        });
                    });
                });

                // Work bullets inline
                    previewContent.querySelectorAll('[data-work-id]').forEach((jobEl) => {
                    const jobId = (jobEl as HTMLElement).dataset.workId!;
                        jobEl.querySelectorAll('li[contenteditable][data-bullet-id]').forEach(li => {
                            li.addEventListener('focus', () => { isInlineEditing = true; });
                            li.addEventListener('blur', () => { isInlineEditing = false; if (latest) requestAnimationFrame(() => renderPreview(latest)); });
                            li.addEventListener('click', () => {
                                lastClickedBullet = li as HTMLElement;
                                console.log('Bullet clicked, stored for deletion:', lastClickedBullet.dataset.bulletId);
                            });
                            li.addEventListener('input', debounce(() => {
                                const text = (li as HTMLElement).innerText.trim().replace(/\u00A0/g, '');
                                console.log('Bullet input changed:', `"${text}"`, 'Length:', text.length);

                                import('../../lib/resumeBuilderService').then(m => {
                                    const curr = m.resumeData.get();

                                    // If the bullet is now empty and there are multiple bullets, delete it
                                    if (text === '' || text.length === 0) {
                                        const currentJob = curr.workExperience.find(j => j.id === jobId);
                                        if (currentJob && currentJob.bulletPoints.length > 1) {
                                            console.log('Auto-deleting empty bullet');
                                            const list = curr.workExperience.map(j => j.id === jobId ? {
                                                ...j,
                                                bulletPoints: j.bulletPoints.filter(bp => bp.id !== (li as HTMLElement).dataset.bulletId)
                                            } : j);
                                            m.updateSection('workExperience', list as any);
                                            return;
                                        }
                                    }

                                    // Otherwise, update the bullet text
                                    const list = curr.workExperience.map(j => j.id === jobId ? {
                                        ...j,
                                        bulletPoints: j.bulletPoints.map(bp =>
                                            bp.id === (li as HTMLElement).dataset.bulletId
                                                ? { ...bp, text: text }
                                                : bp)
                                    } : j);
                                    m.updateSection('workExperience', list as any);
                                });
                            }));
                            li.addEventListener('keydown', (e) => {
                                const key = (e as KeyboardEvent).key;
                                const text = (li as HTMLElement).innerText.trim().replace(/\u00A0/g, ''); // Remove non-breaking spaces
                                console.log('Keydown on bullet:', key, 'Text:', `"${text}"`, 'Length:', text.length);

                                // Handle Enter key - create new bullet point
                                if (key === 'Enter') {
                                    e.preventDefault();
                                    console.log('Creating new bullet point');
                                    import('../../lib/resumeBuilderService').then(m => {
                                        const curr = m.resumeData.get();
                                        const currentJob = curr.workExperience.find(j => j.id === jobId);
                                        if (currentJob) {
                                            const currentBulletIndex = currentJob.bulletPoints.findIndex(bp => bp.id === (li as HTMLElement).dataset.bulletId);
                                            const newBullet = { id: crypto.randomUUID(), text: '' };
                                            const newBullets = [...currentJob.bulletPoints];
                                            newBullets.splice(currentBulletIndex + 1, 0, newBullet);

                                            const list = curr.workExperience.map(j => j.id === jobId ? {
                                                ...j,
                                                bulletPoints: newBullets
                                            } : j);
                                            m.updateSection('workExperience', list as any);

                                            // Focus the new bullet after a brief delay
                                            setTimeout(() => {
                                                const newBulletElement = jobEl.querySelector(`li[data-bullet-id="${newBullet.id}"]`) as HTMLElement;
                                                if (newBulletElement) {
                                                    console.log('Focusing new bullet element');
                                                    newBulletElement.focus();
                                                }
                                            }, 100);
                                        }
                                    });
                                }

                                // Handle Backspace on empty bullet - delete it
                                else if (key === 'Backspace' && (text === '' || text.length === 0)) {
                                    console.log('Attempting to delete empty bullet');
                                    e.preventDefault();
                                    import('../../lib/resumeBuilderService').then(m => {
                                        const curr = m.resumeData.get();
                                        const currentJob = curr.workExperience.find(j => j.id === jobId);
                                        if (currentJob && currentJob.bulletPoints.length > 1) { // Keep at least one bullet
                                            console.log('Deleting bullet point');
                                            const list = curr.workExperience.map(j => j.id === jobId ? {
                                                ...j,
                                                bulletPoints: j.bulletPoints.filter(bp => bp.id !== (li as HTMLElement).dataset.bulletId)
                                            } : j);
                                            m.updateSection('workExperience', list as any);
                                        } else {
                                            console.log('Cannot delete - only one bullet remaining');
                                        }
                                    });
                                }
                            });
                        });

                    const addBtn = jobEl.querySelector('[data-action="add-bullet"]');
                    if (addBtn) {
                        addBtn.addEventListener('click', () => {
                            import('../../lib/resumeBuilderService').then(m => {
                                const curr = m.resumeData.get();
                                const list = curr.workExperience.map(j => j.id === jobId ? { ...j, bulletPoints: [...(j.bulletPoints||[]), { id: crypto.randomUUID(), text: '' }] } : j);
                                m.updateSection('workExperience', list as any);
                            });
                        });
                    }
                    const removeBtn = jobEl.querySelector('[data-action="remove-job"]');
                    if (removeBtn) {
                        removeBtn.addEventListener('click', () => {
                            if (!confirm('Remove this role?')) return;
                            import('../../lib/resumeBuilderService').then(m => m.removeItem('workExperience', jobId));
                        });
                    }
                });

                // Add Job button
                const addJobBtn = document.querySelector('#section-experience [data-action="add-job"]') as HTMLButtonElement | null;
                if (addJobBtn) {
                    addJobBtn.addEventListener('click', () => {
                        import('../../lib/resumeBuilderService').then(m => m.addItem('workExperience', { bulletPoints: [] }));
                    });
                }

                // Remove Education buttons
                previewContent.querySelectorAll('[data-edu-id]').forEach((eduEl) => {
                    const eduId = (eduEl as HTMLElement).dataset.eduId!;
                    const removeBtn = eduEl.querySelector('[data-action="remove-edu"]');
                    if (removeBtn) {
                        removeBtn.addEventListener('click', () => {
                            if (!confirm('Remove this education entry?')) return;
                            import('../../lib/resumeBuilderService').then(m => m.removeItem('education', eduId));
                        });
                    }
                });

                // Add Education button
                const addEduBtn = document.querySelector('#section-education [data-action="add-edu"]') as HTMLButtonElement | null;
                if (addEduBtn) {
                    addEduBtn.addEventListener('click', () => {
                        import('../../lib/resumeBuilderService').then(m => m.addItem('education', { bulletPoints: [] }));
                    });
                }

                // Custom section inline editing (title + bullets like other sections)
                previewContent.querySelectorAll('[data-custom-id]').forEach((customEl) => {
                    const customId = (customEl as HTMLElement).dataset.customId!;
                    const titleEl = customEl.querySelector('[data-custom-field="title"]') as HTMLElement | null;
                    if (titleEl) {
                        titleEl.addEventListener('focus', () => { isInlineEditing = true; });
                        titleEl.addEventListener('blur', () => { isInlineEditing = false; if (latest) requestAnimationFrame(() => renderPreview(latest)); });
                        titleEl.addEventListener('input', debounce(() => {
                            import('../../lib/resumeBuilderService').then(m => {
                                const curr = m.resumeData.get();
                                const list = curr.customSections.map(s => s.id === customId ? { ...s, title: (titleEl as HTMLElement).innerText.trim() } : s);
                                m.updateSection('customSections', list as any);
                            });
                        }));
                    }

                    customEl.querySelectorAll('li[contenteditable][data-bullet-id]').forEach(li => {
                        li.addEventListener('focus', () => { isInlineEditing = true; });
                        li.addEventListener('blur', () => { isInlineEditing = false; if (latest) requestAnimationFrame(() => renderPreview(latest)); });
                        li.addEventListener('input', debounce(() => {
                            const text = (li as HTMLElement).innerText.trim().replace(/\u00A0/g, '');
                            import('../../lib/resumeBuilderService').then(m => {
                                const curr = m.resumeData.get();
                                // If empty and multiple bullets, delete it
                                const section = curr.customSections.find(s => s.id === customId);
                                if ((text === '' || text.length === 0) && section && (section.bulletPoints?.length || 0) > 1) {
                                    const list = curr.customSections.map(s => s.id === customId ? {
                                        ...s,
                                        bulletPoints: (s.bulletPoints || []).filter(bp => bp.id !== (li as HTMLElement).dataset.bulletId)
                                    } : s);
                                    m.updateSection('customSections', list as any);
                                    return;
                                }
                                // Otherwise update
                                const list = curr.customSections.map(s => s.id === customId ? {
                                    ...s,
                                    bulletPoints: (s.bulletPoints || []).map(bp =>
                                        bp.id === (li as HTMLElement).dataset.bulletId
                                            ? { ...bp, text }
                                            : bp)
                                } : s);
                                m.updateSection('customSections', list as any);
                            });
                        }));
                        li.addEventListener('keydown', (e) => {
                            const key = (e as KeyboardEvent).key;
                            const text = (li as HTMLElement).innerText.trim().replace(/\u00A0/g, '');
                            if (key === 'Enter') {
                                e.preventDefault();
                                import('../../lib/resumeBuilderService').then(m => {
                                    const curr = m.resumeData.get();
                                    const current = curr.customSections.find(s => s.id === customId);
                                    if (current) {
                                        const idx = (current.bulletPoints || []).findIndex(bp => bp.id === (li as HTMLElement).dataset.bulletId);
                                        const newBullet = { id: crypto.randomUUID(), text: '' };
                                        const newBullets = [...(current.bulletPoints || [])];
                                        newBullets.splice(idx + 1, 0, newBullet);
                                        const list = curr.customSections.map(s => s.id === customId ? { ...s, bulletPoints: newBullets } : s);
                                        m.updateSection('customSections', list as any);
                                        setTimeout(() => {
                                            const newBulletEl = customEl.querySelector(`li[data-bullet-id="${newBullet.id}"]`) as HTMLElement;
                                            newBulletEl?.focus();
                                        }, 100);
                                    }
                                });
                            } else if (key === 'Backspace' && (text === '' || text.length === 0)) {
                                import('../../lib/resumeBuilderService').then(m => {
                                    const curr = m.resumeData.get();
                                    const current = curr.customSections.find(s => s.id === customId);
                                    if (current && (current.bulletPoints?.length || 0) > 1) {
                                        e.preventDefault();
                                        const list = curr.customSections.map(s => s.id === customId ? {
                                            ...s,
                                            bulletPoints: (s.bulletPoints || []).filter(bp => bp.id !== (li as HTMLElement).dataset.bulletId)
                                        } : s);
                                        m.updateSection('customSections', list as any);
                                    }
                                });
                            }
                        });
                    });
                });




                function openJobPopover(hostEl, jobId) {
                    const pop = document.createElement('div');
                    pop.className = 'inline-popover';
                    pop.innerHTML = `
                      <div class="popover-card" style="min-width: 450px; max-width: 600px;">
                        <h4 class="font-semibold mb-3 text-gray-800 dark:text-gray-200">Edit Work Experience</h4>
                        <div class="grid grid-cols-1 sm:grid-cols-2 gap-3 mb-4">
                          <label class="block text-xs font-medium text-gray-700 dark:text-gray-300">Job Title<input type="text" data-field="jobTitle" class="pop-input"></label>
                          <label class="block text-xs font-medium text-gray-700 dark:text-gray-300">Company<input type="text" data-field="company" class="pop-input"></label>
                          <label class="block text-xs font-medium text-gray-700 dark:text-gray-300">Location<input type="text" data-field="location" class="pop-input"></label>
                          <label class="block text-xs font-medium text-gray-700 dark:text-gray-300">Start Date<input type="month" data-field="startDate" class="pop-input"></label>
                          <label class="block text-xs font-medium text-gray-700 dark:text-gray-300">End Date<input type="month" data-field="endDate" class="pop-input" placeholder="Leave empty for Present"></label>
                        </div>
                        <div class="mb-4">
                          <label class="block text-xs font-medium mb-2 text-gray-700 dark:text-gray-300">Key Achievements & Responsibilities</label>
                          <div id="bullet-points-container" class="space-y-2">
                            <!-- Bullet points will be populated here -->
                          </div>
                          <button type="button" class="mt-2 px-3 py-1 bg-green-100 text-green-700 rounded text-xs hover:bg-green-200 dark:bg-green-900/30 dark:text-green-300 dark:hover:bg-green-800/40" data-action="add-bullet">+ Add Achievement</button>
                        </div>
                        <div class="flex gap-2 pt-3 border-t border-gray-200 dark:border-gray-600">
                          <button class="px-4 py-2 bg-blue-600 text-white rounded text-sm hover:bg-blue-700" data-action="save">Save Changes</button>
                          <button class="px-4 py-2 bg-gray-300 text-gray-700 rounded text-sm hover:bg-gray-400 dark:bg-gray-600 dark:text-gray-200 dark:hover:bg-gray-500 pop-close">Cancel</button>
                          <button class="px-4 py-2 bg-red-100 text-red-700 rounded text-sm hover:bg-red-200 ml-auto dark:bg-red-900/30 dark:text-red-300 dark:hover:bg-red-800/40" data-action="delete">Delete Job</button>
                        </div>
                      </div>`;
                    document.body.appendChild(pop);

                    const bulletContainer = pop.querySelector('#bullet-points-container');
                    let currentBullets: { id: string, text: string }[] = [];

                    const renderBullets = () => {
                        if (bulletContainer) {
                            bulletContainer.innerHTML = currentBullets.map((bullet, index) => `
                                <div class="flex gap-2 items-start">
                                    <textarea
                                        class="pop-input flex-1 min-h-[60px] resize-none"
                                        data-bullet-index="${index}"
                                        placeholder="Describe your achievement or responsibility..."
                                    >${bullet.text || ''}</textarea>
                                    <button type="button" class="px-2 py-1 bg-red-100 text-red-600 rounded text-xs hover:bg-red-200 dark:bg-red-900/30 dark:text-red-300 dark:hover:bg-red-800/40" data-action="remove-bullet" data-bullet-index="${index}">×</button>
                                </div>
                            `).join('');
                        }
                    };

                    import('../../lib/resumeBuilderService').then((m: any) => {
                        const job = m.resumeData.get().workExperience.find(j => j.id === jobId);
                        if (!job) return;

                        // Populate basic fields
                        (pop.querySelector('[data-field="jobTitle"]') as HTMLInputElement).value = job.jobTitle || '';
                        (pop.querySelector('[data-field="company"]') as HTMLInputElement).value = job.company || '';
                        (pop.querySelector('[data-field="location"]') as HTMLInputElement).value = job.location || '';
                        (pop.querySelector('[data-field="startDate"]') as HTMLInputElement).value = job.startDate || '';
                        (pop.querySelector('[data-field="endDate"]') as HTMLInputElement).value = job.endDate === 'Present' ? '' : (job.endDate || '');

                        // Populate bullet points
                        currentBullets = job.bulletPoints || [];
                        if (currentBullets.length === 0) {
                            currentBullets = [{ id: crypto.randomUUID(), text: '' }];
                        }
                        renderBullets();

                        // Real-time updates for basic fields
                        const deb = (fn: (...args: any[]) => void)=>{ let t: ReturnType<typeof setTimeout>|null=null; return (...a:any[])=>{ if(t)clearTimeout(t); t=setTimeout(()=>fn(...a),200);}; };
                        pop.querySelectorAll<HTMLInputElement>('[data-field]').forEach((inp)=>{
                          inp.addEventListener('input', deb(()=>{
                            const field = (inp as HTMLElement).dataset.field as string;
                            const value = (inp as HTMLInputElement).value;
                            const current = m.resumeData.get().workExperience.map((j: any) => j.id === jobId ? { ...j, [field]: value || (field === 'endDate' ? 'Present' : '') } : j);
                            m.updateSection('workExperience', current as any);
                          }));
                        });
                    });

                    // Handle popup actions
                    pop.addEventListener('click', (e) => {
                        const btn = (e.target as HTMLElement).closest('[data-action]') as HTMLButtonElement | null;
                        if (!btn) return;

                        if (btn.dataset.action === 'add-bullet') {
                            currentBullets.push({ id: crypto.randomUUID(), text: '' });
                            renderBullets();
                            // Focus the new textarea
                            if (bulletContainer) {
                                const newTextarea = bulletContainer.querySelector(`[data-bullet-index="${currentBullets.length - 1}"]`) as HTMLTextAreaElement;
                                if (newTextarea) newTextarea.focus();
                            }
                        }

                        else if (btn.dataset.action === 'remove-bullet') {
                            const index = parseInt(btn.dataset.bulletIndex || '0');
                            currentBullets.splice(index, 1);
                            if (currentBullets.length === 0) {
                                currentBullets = [{ id: crypto.randomUUID(), text: '' }];
                            }
                            renderBullets();
                        }

                        else if (btn.dataset.action === 'save') {
                            // Update bullet points from textareas
                            if (bulletContainer) {
                                const textareas = bulletContainer.querySelectorAll('textarea');
                                textareas.forEach((textarea, index) => {
                                    if (currentBullets[index]) {
                                        currentBullets[index].text = (textarea as HTMLTextAreaElement).value.trim();
                                    }
                                });
                            }

                            // Filter out empty bullets
                            currentBullets = currentBullets.filter(bullet => bullet.text.trim() !== '');

                            import('../../lib/resumeBuilderService').then((m: any) => {
                                const current = m.resumeData.get().workExperience.map((j: any) => j.id === jobId ? {
                                    ...j,
                                    bulletPoints: currentBullets
                                } : j);
                                m.updateSection('workExperience', current as any);
                            });
                            pop.remove();
                        }

                        else if (btn.dataset.action === 'delete') {
                            if (confirm('Are you sure you want to delete this work experience?')) {
                                import('../../lib/resumeBuilderService').then((m: any) => {
                                    m.removeItem('workExperience', jobId);
                                });
                                pop.remove();
                            }
                        }
                    });

                    // Handle bullet point text changes
                    pop.addEventListener('input', (e) => {
                        const textarea = e.target as HTMLTextAreaElement;
                        if (textarea.matches('textarea[data-bullet-index]')) {
                            const index = parseInt(textarea.dataset.bulletIndex || '0');
                            if (currentBullets[index]) {
                                currentBullets[index].text = textarea.value;
                            }
                        }
                    });

                    // Close handlers
                    pop.querySelector('.pop-close')?.addEventListener('click', () => pop.remove());

                    // Close on escape key
                    const handleEscape = (e) => {
                        if (e.key === 'Escape') {
                            pop.remove();
                            document.removeEventListener('keydown', handleEscape);
                        }
                    };
                    document.addEventListener('keydown', handleEscape);
                }

                function openEduPopover(hostEl, eduId) {
                    const pop = document.createElement('div');
                    pop.className = 'inline-popover';
                    pop.innerHTML = `
                      <div class="popover-card" style="min-width: 400px;">
                        <h4 class="font-semibold mb-3 text-gray-800 dark:text-gray-200">Edit Education</h4>
                        <div class="grid grid-cols-1 sm:grid-cols-2 gap-3 mb-4">
                          <label class="block text-xs font-medium text-gray-700 dark:text-gray-300">Institution<input type="text" data-field="institution" class="pop-input"></label>
                          <label class="block text-xs font-medium text-gray-700 dark:text-gray-300">Degree<input type="text" data-field="degree" class="pop-input"></label>
                          <label class="block text-xs font-medium text-gray-700 dark:text-gray-300">Field of Study<input type="text" data-field="fieldOfStudy" class="pop-input"></label>
                          <label class="block text-xs font-medium text-gray-700 dark:text-gray-300">Graduation Date<input type="month" data-field="graduationDate" class="pop-input"></label>
                        </div>
                        <div class="mb-4">
                          <label class="block text-xs font-medium mb-2 text-gray-700 dark:text-gray-300">Additional Details (Optional)</label>
                          <textarea data-field="details" class="pop-input" rows="3" placeholder="Relevant coursework, honors, GPA, etc."></textarea>
                        </div>
                        <div class="flex gap-2 pt-3 border-t border-gray-200 dark:border-gray-600">
                          <button class="px-4 py-2 bg-blue-600 text-white rounded text-sm hover:bg-blue-700 pop-close">Save Changes</button>
                          <button class="px-4 py-2 bg-gray-300 text-gray-700 rounded text-sm hover:bg-gray-400 dark:bg-gray-600 dark:text-gray-200 dark:hover:bg-gray-500 pop-close">Cancel</button>
                          <button class="px-4 py-2 bg-red-100 text-red-700 rounded text-sm hover:bg-red-200 ml-auto dark:bg-red-900/30 dark:text-red-300 dark:hover:bg-red-800/40" data-action="delete">Delete Education</button>
                        </div>
                      </div>`;
                    document.body.appendChild(pop);
                    import('../../lib/resumeBuilderService').then((m: any) => {
                        const edu = m.resumeData.get().education.find(e => e.id === eduId);
                        if (!edu) return;
                        (pop.querySelector('[data-field="institution"]') as HTMLInputElement).value = edu.institution || '';
                        (pop.querySelector('[data-field="degree"]') as HTMLInputElement).value = edu.degree || '';
                        (pop.querySelector('[data-field="fieldOfStudy"]') as HTMLInputElement).value = edu.fieldOfStudy || '';
                        (pop.querySelector('[data-field="graduationDate"]') as HTMLInputElement).value = edu.graduationDate || '';
                        (pop.querySelector('[data-field="details"]') as HTMLTextAreaElement).value = edu.details || '';
                        const deb = (fn: (...args: any[]) => void)=>{ let t: ReturnType<typeof setTimeout>|null=null; return (...a:any[])=>{ if(t)clearTimeout(t); t=setTimeout(()=>fn(...a),200);}; };
                        pop.querySelectorAll<HTMLInputElement | HTMLTextAreaElement>('.pop-input').forEach((inp)=>{
                          inp.addEventListener('input', deb(()=>{
                            const field = (inp as HTMLElement).dataset.field as string;
                            const value = (inp as HTMLInputElement).value;
                            const current = m.resumeData.get().education.map((e: any) => e.id === eduId ? { ...e, [field]: value } : e);
                            m.updateSection('education', current as any);
                          }));
                        });
                    });

                    // Handle popup actions
                    pop.addEventListener('click', (e) => {
                        const btn = (e.target as HTMLElement).closest('[data-action]') as HTMLButtonElement | null;
                        if (btn?.dataset.action === 'delete') {
                            if (confirm('Are you sure you want to delete this education entry?')) {
                                import('../../lib/resumeBuilderService').then((m: any) => {
                                    m.removeItem('education', eduId);
                                });
                                pop.remove();
                            }
                        }
                    });

                    pop.querySelectorAll('.pop-close').forEach(btn => {
                        btn.addEventListener('click', () => pop.remove());
                    });

                    // Close on escape key
                    const handleEscape = (e) => {
                        if (e.key === 'Escape') {
                            pop.remove();
                            document.removeEventListener('keydown', handleEscape);
                        }
                    };
                    document.addEventListener('keydown', handleEscape);
                }

                // Shared contact popover utility (used by both header edit and toolbar)
                function openContactPopover(hostEl) {
                    const pop = document.createElement('div');
                    pop.className = 'inline-popover';
                    pop.innerHTML = `
                      <div class="popover-card">
                        <div class="grid grid-cols-1 sm:grid-cols-2 gap-2">
                          <label class="block text-xs">Full Name<input type="text" data-field="fullName" class="pop-input" aria-label="Full Name"></label>
                          <label class="block text-xs">Role/Title<input type="text" data-field="role" class="pop-input" aria-label="Role/Title"></label>
                          <label class="block text-xs">Email<input type="email" data-field="email" class="pop-input" aria-label="Email"></label>
                          <label class="block text-xs">Phone<input type="tel" data-field="phone" class="pop-input" aria-label="Phone"></label>
                          <label class="block text-xs">Location<input type="text" data-field="location" class="pop-input" aria-label="Location"></label>
                          <label class="block text-xs">LinkedIn<input type="url" data-field="linkedin" class="pop-input" aria-label="LinkedIn"></label>
                          <label class="block text-xs">Portfolio<input type="url" data-field="portfolio" class="pop-input" aria-label="Portfolio"></label>
                        </div>
                        <div class="mt-2 flex justify-end gap-2">
                          <button class="pop-close text-xs px-2 py-1 rounded-md border border-gray-200 dark:border-gray-700">Close</button>
                        </div>
                      </div>`;
                    document.body.appendChild(pop);
                    const rect = hostEl.getBoundingClientRect();
                    pop.style.left = `${Math.round(rect.left + window.scrollX)}px`;
                    pop.style.top = `${Math.round(rect.top + window.scrollY + rect.height + 6)}px`;
                    import('../../lib/resumeBuilderService').then((m: any) => {
                        const contact = m.resumeData.get().contactInfo;
                        if (!contact) return;
                        (pop.querySelector('[data-field="fullName"]') as HTMLInputElement).value = contact.fullName || '';
                        (pop.querySelector('[data-field="role"]') as HTMLInputElement).value = contact.role || '';
                        (pop.querySelector('[data-field="email"]') as HTMLInputElement).value = contact.email || '';
                        (pop.querySelector('[data-field="phone"]') as HTMLInputElement).value = contact.phone || '';
                        (pop.querySelector('[data-field="location"]') as HTMLInputElement).value = contact.location || '';
                        (pop.querySelector('[data-field="linkedin"]') as HTMLInputElement).value = contact.linkedin || '';
                        (pop.querySelector('[data-field="portfolio"]') as HTMLInputElement).value = contact.portfolio || '';
                        const deb = (fn: (...args: any[]) => void)=>{ let t: ReturnType<typeof setTimeout>|null=null; return (...a:any[])=>{ if(t)clearTimeout(t); t=setTimeout(()=>fn(...a),200);}; };
                        pop.querySelectorAll<HTMLInputElement | HTMLTextAreaElement>('.pop-input').forEach((inp)=>{
                          inp.addEventListener('input', deb(()=>{
                            const field = (inp as HTMLElement).dataset.field as string;
                            const value = (inp as HTMLInputElement).value;
                            m.updateContactInfo(field, value);
                          }));
                        });
                    });
                    pop.querySelector('.pop-close')?.addEventListener('click', () => pop.remove());
                }

                // Dates-only popover utility (start/end or graduation)
                function openDatesPopover(hostEl: HTMLElement, type: 'work' | 'edu', id: string) {
                    const pop = document.createElement('div');
                    pop.className = 'inline-popover';
                    const isWork = type === 'work';
                    pop.innerHTML = `
                      <div class="popover-card">
                        <div class="grid grid-cols-1 sm:grid-cols-2 gap-2">
                          ${isWork ? `
                            <label class="block text-xs">Start<input type="month" data-field="startDate" class="pop-input" aria-label="Start date"></label>
                            <label class="block text-xs">End<input type="month" data-field="endDate" class="pop-input" aria-label="End date"></label>
                            <label class="block text-xs col-span-2">
                              <input type="checkbox" data-field="present" class="mr-1 align-middle"> <span class="align-middle">Present</span>
                            </label>
                          ` : `
                            <label class="block text-xs col-span-2">Graduation<input type="month" data-field="graduationDate" class="pop-input" aria-label="Graduation date"></label>
                          `}
                        </div>
                        <div class="mt-2 flex justify-end gap-2">
                          <button class="pop-close text-xs px-2 py-1 rounded-md border border-gray-200 dark:border-gray-700">Close</button>
                        </div>
                      </div>`;
                    document.body.appendChild(pop);
                    const rect = hostEl.getBoundingClientRect();
                    pop.style.left = `${Math.round(rect.left + window.scrollX)}px`;
                    pop.style.top = `${Math.round(rect.top + window.scrollY + rect.height + 6)}px`;

                    import('../../lib/resumeBuilderService').then((m: any) => {
                        const state = m.resumeData.get();
                        if (isWork) {
                            const job = state.workExperience.find((j: any) => j.id === id);
                            if (!job) return;
                            (pop.querySelector('[data-field="startDate"]') as HTMLInputElement).value = job.startDate || '';
                            const endInp = pop.querySelector('[data-field="endDate"]') as HTMLInputElement;
                            const presentChk = pop.querySelector('[data-field="present"]') as HTMLInputElement;
                            const isPresent = job.endDate === 'Present';
                            if (endInp) endInp.value = typeof job.endDate === 'string' && !isPresent ? job.endDate : '';
                            if (presentChk) presentChk.checked = isPresent;

                            const deb = (fn: (...args: any[]) => void)=>{ let t: ReturnType<typeof setTimeout>|null=null; return (...a:any[])=>{ if(t)clearTimeout(t); t=setTimeout(()=>fn(...a),200);}; };
                            pop.querySelectorAll<HTMLInputElement>('.pop-input').forEach((inp)=>{
                                if ((inp as HTMLInputElement).type === 'checkbox') {
                                    inp.addEventListener('change', ()=>{
                                        const checked = (inp as HTMLInputElement).checked;
                                        const next = state.workExperience.map((j: any)=> j.id === id ? { ...j, endDate: checked ? 'Present' : '' } : j);
                                        m.updateSection('workExperience', next as any);
                                    });
                                } else {
                                    inp.addEventListener('input', deb(()=>{
                                        const field = (inp as HTMLElement).dataset.field as 'startDate'|'endDate';
                                        const value = (inp as HTMLInputElement).value;
                                        const curr = m.resumeData.get();
                                        const next = curr.workExperience.map((j: any)=> j.id === id ? { ...j, [field]: value } : j);
                                        m.updateSection('workExperience', next as any);
                                    }));
                                }
                            });
                        } else {
                            const edu = state.education.find((e: any) => e.id === id);
                            if (!edu) return;
                            (pop.querySelector('[data-field="graduationDate"]') as HTMLInputElement).value = edu.graduationDate || '';
                            const deb = (fn: (...args: any[]) => void)=>{ let t: ReturnType<typeof setTimeout>|null=null; return (...a:any[])=>{ if(t)clearTimeout(t); t=setTimeout(()=>fn(...a),200);}; };
                            pop.querySelector('[data-field="graduationDate"]')?.addEventListener('input', deb(()=>{
                                const value = (pop.querySelector('[data-field="graduationDate"]') as HTMLInputElement).value;
                                const curr = m.resumeData.get();
                                const next = curr.education.map((e: any)=> e.id === id ? { ...e, graduationDate: value } : e);
                                m.updateSection('education', next as any);
                            }));
                        }
                    });

                    pop.querySelector('.pop-close')?.addEventListener('click', () => pop.remove());
                }

                // Wire edit buttons (contact, job, education)
                // Popup functions removed - using floating toolbar instead

                // Floating Toolbar Logic (click-to-select; stays open until dismissed)
                const toolbar = document.getElementById('floating-toolbar') as HTMLElement | null;
                let toolbarWired = (previewContent as any)._toolbarWired === true;

                if (toolbar && !toolbarWired) {
                    let activeTarget: HTMLElement | null = null;

                    const getTypeAndIds = (target: HTMLElement) => {
                        const workId = target.getAttribute('data-work-id');
                        const eduId = target.getAttribute('data-edu-id');
                        const customId = target.getAttribute('data-custom-id');
                        let sectionType = target.dataset.sectionType as 'work' | 'edu' | 'custom' | 'header' | 'summary' | 'skills';

                        // Infer type if not directly present on the element
                        if (!sectionType) {
                            if (workId) sectionType = 'work';
                            else if (eduId) sectionType = 'edu';
                            else if (customId) sectionType = 'custom';
                            else {
                                // Check parent section if it's a generic container
                                const parentSection = target.closest('[data-section-type]') as HTMLElement | null;
                                if (parentSection) {
                                    sectionType = parentSection.dataset.sectionType as any;
                                }
                            }
                        }

                        let type: 'work'|'edu'|'custom'|'contact'|'summary'|'skills'|undefined;

                        switch (sectionType) {
                            case 'work':
                                type = 'work';
                                break;
                            case 'edu':
                                type = 'edu';
                                break;
                            case 'custom':
                                type = 'custom';
                                break;
                            case 'header':
                                type = 'contact';
                                break;
                            case 'summary':
                                type = 'summary';
                                break;
                            case 'skills':
                                type = 'skills';
                                break;
                            default:
                                type = undefined;
                        }

                        return { type, workId, eduId, customId };
                    };

                    const positionToolbar = () => {
                        if (!activeTarget || !toolbar) return;

                        // Get the positioned parent container (the one with relative positioning)
                        const positionedParent = toolbar.offsetParent;
                        if (!positionedParent) return;

                        const targetRect = activeTarget.getBoundingClientRect();
                        const parentRect = positionedParent.getBoundingClientRect();

                        // Get toolbar dimensions
                        const tW = toolbar.offsetWidth || 0;
                        const tH = toolbar.offsetHeight || 0;

                        // Calculate position relative to the positioned parent
                        // Center horizontally over the target element
                        let left = targetRect.left - parentRect.left + (targetRect.width / 2) - (tW / 2);

                        // Position above the target element with some spacing
                        let top = targetRect.top - parentRect.top - tH - 12;

                        // If not enough room above, place below the target
                        if (top < 12) {
                            top = targetRect.top - parentRect.top + targetRect.height + 12;
                        }

                        // Get the actual content boundaries (not the full wrapper)
                        const resumeContent = document.getElementById('resume-preview-content');
                        if (resumeContent) {
                            const contentRect = resumeContent.getBoundingClientRect();
                            const contentLeft = contentRect.left - parentRect.left;
                            const contentRight = contentRect.right - parentRect.left;
                            const contentTop = contentRect.top - parentRect.top;
                            const contentBottom = contentRect.bottom - parentRect.top;

                            // Clamp horizontally within content boundaries
                            const minLeft = Math.max(12, contentLeft + 12);
                            const maxLeft = Math.min(parentRect.width - tW - 12, contentRight - tW - 12);
                            left = Math.max(minLeft, Math.min(left, maxLeft));

                            // Clamp vertically within content boundaries
                            // Allow toolbar to sit above the resume content (prevent overlapping header text)
                            const minTop = 12;
                            const maxTop = Math.min(parentRect.height - tH - 12, contentBottom - tH - 12);
                            top = Math.max(minTop, Math.min(top, maxTop));
                        } else {
                            // Fallback to parent boundaries
                            left = Math.max(12, Math.min(left, parentRect.width - tW - 12));
                            top = Math.max(12, Math.min(top, parentRect.height - tH - 12));
                        }

                        // Contextual button visibility handled in updateToolbarContextualButtons()

                        toolbar.style.left = `${Math.round(left)}px`;
                        toolbar.style.top = `${Math.round(top)}px`;
                    };

                    // Show/hide context-sensitive toolbar buttons based on current selection
                    const updateToolbarContextualButtons = () => {
                        if (!activeTarget) return;
                        const delBulletBtn = toolbar.querySelector('[data-action="delete-bullet"]') as HTMLButtonElement | null;
                        const moveUpBtn = toolbar.querySelector('[data-action="move-up"]') as HTMLButtonElement | null;
                        const moveDownBtn = toolbar.querySelector('[data-action="move-down"]') as HTMLButtonElement | null;

                        // Also reference groups/buttons we may need to toggle
                        const bulletDropdown = toolbar.querySelector('.bullet-dropdown') as HTMLElement | null;
                        const moveDropdown = toolbar.querySelector('.move-dropdown') as HTMLElement | null;
                        const duplicateBtn = toolbar.querySelector('button[data-action="duplicate"]') as HTMLElement | null;
                        const addBtn = toolbar.querySelector('button[data-action="add"]') as HTMLElement | null;
                        const fieldsDropdown = toolbar.querySelector('.fields-dropdown') as HTMLElement | null;

                        // Delete bullet visibility: hide if no bullets or only one bullet
                        const bulletsCount = activeTarget.querySelectorAll('li[contenteditable][data-bullet-id]').length;
                        if (delBulletBtn) {
                            delBulletBtn.classList.toggle('hidden', bulletsCount <= 1);
                        }

                        // Move Up/Down: only for reorderable entries (work/edu/custom)
                        const { type } = getTypeAndIds(activeTarget);
                        let idx = -1, total = 0;

                        if (type === 'work') {
                            const id = activeTarget.getAttribute('data-work-id');
                            const items = Array.from(document.querySelectorAll('[data-work-id]')) as HTMLElement[];
                            idx = items.findIndex(el => el.getAttribute('data-work-id') === id);
                            total = items.length;
                        } else if (type === 'edu') {
                            const id = activeTarget.getAttribute('data-edu-id');
                            const items = Array.from(document.querySelectorAll('[data-edu-id]')) as HTMLElement[];
                            idx = items.findIndex(el => el.getAttribute('data-edu-id') === id);
                            total = items.length;
                        } else if (type === 'custom') {
                            const id = activeTarget.getAttribute('data-custom-id');
                            const items = Array.from(document.querySelectorAll('[data-custom-id]')) as HTMLElement[];
                            idx = items.findIndex(el => el.getAttribute('data-custom-id') === id);
                            total = items.length;
                        } else {
                            // Non-reorderable containers (summary/skills/header)
                            idx = -1; total = 0;
                        }

                        const canMoveUp = idx > 0;
                        const canMoveDown = total > 0 && idx > -1 && idx < total - 1;

                        if (moveUpBtn) moveUpBtn.classList.toggle('hidden', !canMoveUp);
                        if (moveDownBtn) moveDownBtn.classList.toggle('hidden', !canMoveDown);

                        // Contextual visibility for groups and actions:
                        const isReorderable = type === 'work' || type === 'edu' || type === 'custom';
                        const isBio = type === 'contact';
                        const hasEntries = type === 'work' || type === 'edu' || type === 'custom';
                        // Hide entry button for summary, skills, and bio sections
                        const shouldHideEntryButton = type === 'summary' || type === 'skills' || type === 'contact';

                        if (bulletDropdown) bulletDropdown.classList.toggle('hidden', false);
                        if (moveDropdown) moveDropdown.classList.toggle('hidden', false);
                        if (addBtn) addBtn.classList.toggle('hidden', !hasEntries || shouldHideEntryButton);
                        if (fieldsDropdown) fieldsDropdown.classList.toggle('hidden', !isBio);
                    };

                    const setToolbarForTarget = (target: HTMLElement) => {
                        // Remove highlight from previously active target
                        if (activeTarget) {
                            activeTarget.classList.remove('item-selected');
                        }

                        activeTarget = target;
                        activeTarget.classList.add('item-selected');

                        const { type, workId, eduId, customId } = getTypeAndIds(target);
                        if (type) {
                            toolbar.dataset.targetType = type;
                            toolbar.setAttribute('data-target-type', type); // Also set as attribute for CSS
                        } else {
                            delete (toolbar.dataset as any).targetType;
                            toolbar.removeAttribute('data-target-type');
                        }
                        if (workId) toolbar.dataset.workId = workId; else delete (toolbar.dataset as any).workId;
                        if (eduId) toolbar.dataset.eduId = eduId; else delete (toolbar.dataset as any).eduId;
                        if (customId) toolbar.dataset.customId = customId; else delete (toolbar.dataset as any).customId;

                        // Show toolbar first, then position it after a brief delay to ensure dimensions are available
                        toolbar.classList.remove('hidden');
                        requestAnimationFrame(() => {
                            positionToolbar();
                            updateToolbarContextualButtons();
                        });
                    };

                    const clearToolbarSelection = () => {
                        if (activeTarget) {
                            activeTarget.classList.remove('item-selected');
                        }
                        activeTarget = null;
                        toolbar.classList.add('hidden');
                        delete (toolbar.dataset as any).targetType;
                        delete (toolbar.dataset as any).workId;
                        delete (toolbar.dataset as any).eduId;
                        delete (toolbar.dataset as any).customId;
                    };

                    const populateBioFields = () => {
                        const fieldsList = toolbar.querySelector('#bio-fields-list');
                        if (!fieldsList) return;

                        const { contactInfo } = resumeData.get();
                        const allFields = {
                            email: 'Email',
                            phone: 'Phone',
                            location: 'Location',
                            linkedin: 'LinkedIn',
                            portfolio: 'Portfolio'
                        };

                        fieldsList.innerHTML = Object.entries(allFields).map(([key, label]) => {
                            const isVisible = !!contactInfo[key];
                            return `
                                <label class="flex items-center justify-between py-1.5 px-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700/60 cursor-pointer">
                                    <span class="text-gray-800 dark:text-gray-200">${label}</span>
                                    <input type="checkbox" data-bio-field="${key}" class="h-4 w-4 rounded bg-gray-200 dark:bg-gray-600 border-gray-300 dark:border-gray-500 text-indigo-600 focus:ring-indigo-500" ${isVisible ? 'checked' : ''}>
                                </label>
                            `;
                        }).join('');
                    };

                    // Select item by click; keep toolbar open
                    previewContent.addEventListener('click', (e) => {
                        const target = (e.target as HTMLElement).closest('.item-container') as HTMLElement | null;
                        if (target) {
                            setToolbarForTarget(target);
                            if (getTypeAndIds(target).type === 'contact') {
                                populateBioFields();
                            }
                            return;
                        }

                        // Track bullet clicks for delete bullet functionality
                        const clickedBullet = (e.target as HTMLElement).closest('li[contenteditable][data-bullet-id]') as HTMLElement | null;
                        if (clickedBullet) {
                            lastClickedBullet = clickedBullet;
                            console.log('Bullet clicked:', clickedBullet.dataset.bulletId);

                            // Also ensure the toolbar is shown for the parent container
                            const parentContainer = clickedBullet.closest('.item-container') as HTMLElement | null;
                            if (parentContainer) {
                                setToolbarForTarget(parentContainer);
                            }
                        }
                    });

                    // Dismiss when clicking outside toolbar and not on an item
                    document.addEventListener('click', (e) => {
                        const el = e.target as HTMLElement;
                        if (toolbar.contains(el)) return; // clicking inside toolbar should not close it
                        const inItem = !!el.closest('.item-container');
// Open contact popover on double-click in header/bio section for easy add/remove fields
                    previewContent.addEventListener('dblclick', (e) => {
                        const headerEl = (e.target as HTMLElement).closest('[data-section-type="header"]') as HTMLElement | null;
                        if (headerEl) {
                            openContactPopover(headerEl);
                        }
                    });
                        const inPreview = !!el.closest('#resume-preview-content');
                        if (!inPreview || !inItem) {
                            clearToolbarSelection();
                        }
                    });

                    // Escape key closes toolbar
                    document.addEventListener('keydown', (e) => {
                        if ((e as KeyboardEvent).key === 'Escape') {
                            clearToolbarSelection();
                        }
                    });

                    // Reposition on resize/scroll
                    window.addEventListener('resize', positionToolbar);
                    window.addEventListener('scroll', positionToolbar, true);

                    // Toolbar actions (modernized + contextual like the reference)
                    toolbar.addEventListener('click', (e) => {
                        const btn = (e.target as HTMLElement).closest('button[data-action]') as HTMLButtonElement | null;
                        if (!btn) return;
                        const action = btn.dataset.action as 'add' | 'add-bullet' | 'delete-bullet' | 'bullets' | 'delete' | 'ai' | 'edit' | 'duplicate' | 'move-up' | 'move-down' | 'close' | 'text' | 'date' | 'settings' | 'job-title' | 'company' | 'location' | 'contact-fields' | 'add-bio-field';
                        const type = toolbar.dataset.targetType as 'work' | 'edu' | 'custom' | 'contact' | undefined;
                        console.log('Toolbar action:', action, 'Type:', type, 'WorkId:', toolbar.dataset.workId);
                        if (!type) return;

                        import('../../lib/resumeBuilderService').then(m => {
                            if (action === 'close') {
                                clearToolbarSelection();
                                return;
                            }

                            if (action === 'delete') {
                                if (type === 'work' && toolbar.dataset.workId) {
                                    m.removeItem('workExperience', toolbar.dataset.workId);
                                    clearToolbarSelection();
                                } else if (type === 'edu' && toolbar.dataset.eduId) {
                                    m.removeItem('education', toolbar.dataset.eduId);
                                    clearToolbarSelection();
                                } else if (type === 'custom' && toolbar.dataset.customId) {
                                    m.removeItem('customSections', toolbar.dataset.customId);
                                    clearToolbarSelection();
                                }
                                return;
                            }

                            if (action === 'add') {
                                // Contextual: add an entry to the section (like "+ Entry")
                                if (type === 'work') {
                                    m.addItem('workExperience', { bulletPoints: [] });
                                } else if (type === 'edu') {
                                    m.addItem('education');
                                } else if (type === 'custom') {
                                    m.addItem('customSections');
                                }
                                return;
                            }

                            if (action === 'bullets') {
                                console.log('Bullets action clicked');
                                if (activeTarget && type === 'work') {
                                    const firstBullet = activeTarget.querySelector('li[contenteditable]') as HTMLElement | null;
                                    if (firstBullet) {
                                        console.log('Focusing first bullet point');
                                        firstBullet.focus();
                                    }
                                }
                                return;
                            }

                            if (action === 'add-bullet') {
                                console.log('Add bullet clicked for type:', type);
                                // Add bullet point to work experience
                                if (type === 'work' && toolbar.dataset.workId) {
                                    const curr = m.resumeData.get();
                                    console.log('Current work experience:', curr.workExperience);
                                    const list = curr.workExperience.map(j => j.id === toolbar.dataset.workId ? {
                                        ...j,
                                        bulletPoints: [...(j.bulletPoints || []), { id: crypto.randomUUID(), text: '' }]
                                    } : j);
                                    m.updateSection('workExperience', list as any);
                                    console.log('Updated work experience with new bullet');

                                    // Focus the new bullet point after a brief delay
                                    setTimeout(() => {
                                        const newBullet = activeTarget?.querySelector('li[contenteditable]:last-child') as HTMLElement;
                                        if (newBullet) {
                                            console.log('Focusing new bullet point');
                                            newBullet.focus();
                                        }
                                    }, 100);
                                }
                                // Add bullet point to education
                                else if (type === 'edu' && toolbar.dataset.eduId) {
                                    const curr = m.resumeData.get();
                                    console.log('Current education:', curr.education);
                                    const list = curr.education.map(e => e.id === toolbar.dataset.eduId ? {
                                        ...e,
                                        bulletPoints: [...(e.bulletPoints || []), { id: crypto.randomUUID(), text: '' }]
                                    } : e);
                                    m.updateSection('education', list as any);
                                    console.log('Updated education with new bullet');

                                    // Focus the new bullet point after a brief delay
                                    setTimeout(() => {
                                        const newBullet = activeTarget?.querySelector('li[contenteditable]:last-child') as HTMLElement;
                                        if (newBullet) {
                                            console.log('Focusing new bullet point');
                                            newBullet.focus();
                                        }
                                    }, 100);
                                }
                                // Add bullet to custom section
                                else if (type === 'custom' && toolbar.dataset.customId) {
                                    const curr = m.resumeData.get();
                                    const list = curr.customSections.map(s => s.id === toolbar.dataset.customId ? {
                                        ...s,
                                        bulletPoints: [...(s.bulletPoints || []), { id: crypto.randomUUID(), text: '' }]
                                    } : s);
                                    m.updateSection('customSections', list as any);
                                    setTimeout(() => {
                                        const newBullet = activeTarget?.querySelector('li[contenteditable]:last-child') as HTMLElement;
                                        newBullet?.focus();
                                    }, 100);
                                }
                                return;
                            }

                            if (action === 'delete-bullet') {
                                console.log('Delete bullet clicked');
                                if (activeTarget && (type === 'work' || type === 'custom' || type === 'edu')) {
                                    // Use the last clicked bullet point or fallback to focused element
                                    const targetBullet = lastClickedBullet || document.activeElement as HTMLElement;
                                    console.log('Target bullet:', targetBullet, 'Last clicked:', lastClickedBullet, 'Focused:', document.activeElement);

                                    if (targetBullet && targetBullet.matches('li[contenteditable][data-bullet-id]')) {
                                        const bulletId = targetBullet.dataset.bulletId;
                                        const curr = m.resumeData.get();
                                        if (type === 'work' && toolbar.dataset.workId) {
                                            const workId = toolbar.dataset.workId;
                                            const currentJob = curr.workExperience.find(j => j.id === workId);
                                            if (bulletId && currentJob) {
                                                // Check if bullet has text
                                                const bulletText = currentJob.bulletPoints?.find(bp => bp.id === bulletId)?.text?.trim() || '';
                                                if (!bulletText || (currentJob.bulletPoints?.length || 0) > 1) {
                                                    const list = curr.workExperience.map(j => j.id === workId ? { ...j, bulletPoints: j.bulletPoints.filter(bp => bp.id !== bulletId) } : j);
                                                    m.updateSection('workExperience', list as any);
                                                    lastClickedBullet = null;
                                                } else {
                                                    alert('Cannot delete the last bullet point. At least one bullet point is required.');
                                                }
                                            }
                                        } else if (type === 'edu' && toolbar.dataset.eduId) {
                                            const eduId = toolbar.dataset.eduId;
                                            const currentEdu = curr.education.find(e => e.id === eduId);
                                            if (bulletId && currentEdu) {
                                                // Check if bullet has text
                                                const bulletText = currentEdu.bulletPoints?.find(bp => bp.id === bulletId)?.text?.trim() || '';
                                                if (!bulletText || (currentEdu.bulletPoints?.length || 0) > 1) {
                                                    const list = curr.education.map(e => e.id === eduId ? { ...e, bulletPoints: (e.bulletPoints || []).filter(bp => bp.id !== bulletId) } : e);
                                                    m.updateSection('education', list as any);
                                                    lastClickedBullet = null;
                                                } else {
                                                    alert('Cannot delete the last bullet point. At least one bullet point is required.');
                                                }
                                            }
                                        } else if (type === 'custom' && toolbar.dataset.customId) {
                                            const customId = toolbar.dataset.customId;
                                            const current = curr.customSections.find(s => s.id === customId);
                                            if (bulletId && current) {
                                                // Check if bullet has text
                                                const bulletText = current.bulletPoints?.find(bp => bp.id === bulletId)?.text?.trim() || '';
                                                if (!bulletText || (current.bulletPoints?.length || 0) > 1) {
                                                    const list = curr.customSections.map(s => s.id === customId ? { ...s, bulletPoints: (s.bulletPoints || []).filter(bp => bp.id !== bulletId) } : s);
                                                    m.updateSection('customSections', list as any);
                                                    lastClickedBullet = null;
                                                } else {
                                                    alert('Cannot delete the last bullet point. At least one bullet point is required.');
                                                }
                                            }
                                        }
                                    } else {
                                        console.log('No bullet point is selected. Click on a bullet point first.');
                                        alert('Please click on a bullet point first, then use the Delete Bullet button.');
                                    }
                                }
                                return;
                            }

                            if (action === 'add-bio-field') {
                                const newFieldName = prompt('Enter new field name (e.g., Website)');
                                if (newFieldName) {
                                    const key = newFieldName.toLowerCase().replace(/\s/g, '');
                                    m.updateContactInfo(key, `[${newFieldName}]`);
                                    populateBioFields();
                                }
                                return;
                            }

                            if (action === 'edit' || action === 'settings') {
                                if (activeTarget) {
                                    const editableElement = activeTarget.querySelector('[contenteditable="true"]') as HTMLElement | null;
                                    if (editableElement) {
                                        editableElement.focus();
                                    }
                                }
                                return;
                            }

                            if (action === 'text') {
                                console.log('Text action clicked');
                                if (activeTarget) {
                                    // For work experience, focus on job title first
                                    if (type === 'work') {
                                        const jobTitle = activeTarget.querySelector('[data-work-field="jobTitle"]') as HTMLElement | null;
                                        if (jobTitle) {
                                            console.log('Focusing job title');
                                            jobTitle.focus();
                                            return;
                                        }
                                    }
                                    // For education, focus on degree
                                    if (type === 'edu') {
                                        const degree = activeTarget.querySelector('[data-edu-field="degree"]') as HTMLElement | null;
                                        if (degree) {
                                            console.log('Focusing degree');
                                            degree.focus();
                                            return;
                                        }
                                    }
                                    // For custom, focus on title first
                                    if (type === 'custom') {
                                        const title = activeTarget.querySelector('[data-custom-field="title"]') as HTMLElement | null;
                                        if (title) {
                                            title.focus();
                                            return;
                                        }
                                    }
                                    // Fallback to any contenteditable
                                    const ce = activeTarget.querySelector('[contenteditable="true"]') as HTMLElement | null
                                        || activeTarget.querySelector('li[contenteditable]') as HTMLElement | null;
                                    if (ce) {
                                        console.log('Focusing fallback contenteditable');
                                        ce.focus();
                                    }
                                }
                                return;
                            }

                            if (action === 'date') {
                                console.log('Date action clicked');
                                if (activeTarget) {
                                    // For work experience, focus on start date first
                                    if (type === 'work') {
                                        const startDate = activeTarget.querySelector('[data-work-field="startDate"]') as HTMLElement | null;
                                        if (startDate) {
                                            console.log('Focusing start date');
                                            startDate.focus();
                                            return;
                                        }
                                    }
                                    // For education, focus on graduation date
                                    if (type === 'edu') {
                                        const gradDate = activeTarget.querySelector('[data-edu-field="graduationDate"]') as HTMLElement | null;
                                        if (gradDate) {
                                            console.log('Focusing graduation date');
                                            gradDate.focus();
                                            return;
                                        }
                                    }
                                    // Fallback to any date field
                                    const dateElement = activeTarget.querySelector('[data-work-field*="Date"], [data-edu-field*="Date"]') as HTMLElement | null;
                                    if (dateElement) {
                                        console.log('Focusing fallback date element');
                                        dateElement.focus();
                                    }
                                }
                                return;
                            }

                            if (action === 'job-title') {
                                console.log('Job title action clicked');
                                if (activeTarget && type === 'work') {
                                    const jobTitle = activeTarget.querySelector('[data-work-field="jobTitle"]') as HTMLElement | null;
                                    if (jobTitle) {
                                        console.log('Focusing job title field');
                                        jobTitle.focus();
                                    }
                                } else if (activeTarget && type === 'edu') {
                                    const degree = activeTarget.querySelector('[data-edu-field="degree"]') as HTMLElement | null;
                                    if (degree) {
                                        console.log('Focusing degree field');
                                        degree.focus();
                                    }
                                }
                                return;
                            }

                            if (action === 'company') {
                                console.log('Company action clicked');
                                if (activeTarget && type === 'work') {
                                    const company = activeTarget.querySelector('[data-work-field="company"]') as HTMLElement | null;
                                    if (company) {
                                        console.log('Focusing company field');
                                        company.focus();
                                    }
                                } else if (activeTarget && type === 'edu') {
                                    const institution = activeTarget.querySelector('[data-edu-field="institution"]') as HTMLElement | null;
                                    if (institution) {
                                        console.log('Focusing institution field');
                                        institution.focus();
                                    }
                                }
                                return;
                            }

                            if (action === 'location') {
                                console.log('Location action clicked');
                                if (activeTarget && type === 'work') {
                                    const location = activeTarget.querySelector('[data-work-field="location"]') as HTMLElement | null;
                                    if (location) {
                                        console.log('Focusing location field');
                                        location.focus();
                                    }
                                }
                                return;
                            }

                            if (action === 'duplicate') {
                                const state = m.resumeData.get();
                                if (type === 'work' && toolbar.dataset.workId) {
                                    const list = [...state.workExperience];
                                    const idx = list.findIndex(i => i.id === toolbar.dataset.workId);
                                    if (idx > -1) {
                                        const dup = JSON.parse(JSON.stringify(list[idx]));
                                        dup.id = crypto.randomUUID();
                                        if (Array.isArray(dup.bulletPoints)) {
                                            dup.bulletPoints = dup.bulletPoints.map((bp: any) => ({ ...bp, id: crypto.randomUUID() }));
                                        }
                                        list.splice(idx + 1, 0, dup);
                                        m.updateSection('workExperience', list as any);
                                        requestAnimationFrame(positionToolbar);
                                    }
                                } else if (type === 'edu' && toolbar.dataset.eduId) {
                                    const list = [...state.education];
                                    const idx = list.findIndex(i => i.id === toolbar.dataset.eduId);
                                    if (idx > -1) {
                                        const dup = { ...list[idx], id: crypto.randomUUID() };
                                        list.splice(idx + 1, 0, dup);
                                        m.updateSection('education', list as any);
                                        requestAnimationFrame(positionToolbar);
                                    }
                                } else if (type === 'custom' && toolbar.dataset.customId) {
                                    const list = [...state.customSections];
                                    const idx = list.findIndex(i => i.id === toolbar.dataset.customId);
                                    if (idx > -1) {
                                        const dup = { ...list[idx], id: crypto.randomUUID() };
                                        list.splice(idx + 1, 0, dup);
                                        m.updateSection('customSections', list as any);
                                        requestAnimationFrame(positionToolbar);
                                    }
                                }
                                return;
                            }

                            if (action === 'move-up') {
                                const state = m.resumeData.get();
                                if (type === 'work' && toolbar.dataset.workId) {
                                    const list = [...state.workExperience];
                                    const currentIndex = list.findIndex(item => item.id === toolbar.dataset.workId);
                                    if (currentIndex > 0) {
                                        // Animate the reorder
                                        const itemElement = previewContent.querySelector(`[data-work-id="${toolbar.dataset.workId}"]`) as HTMLElement;
                                        if (itemElement) {
                                            itemElement.classList.add('slide-up-smooth');
                                            setTimeout(() => itemElement.classList.remove('slide-up-smooth'), 400);
                                        }

                                        // Swap items
                                        [list[currentIndex], list[currentIndex - 1]] = [list[currentIndex - 1], list[currentIndex]];
                                        m.updateSection('workExperience', list as any);
                                        requestAnimationFrame(positionToolbar);
                                    }
                                } else if (type === 'edu' && toolbar.dataset.eduId) {
                                    const list = [...state.education];
                                    const currentIndex = list.findIndex(item => item.id === toolbar.dataset.eduId);
                                    if (currentIndex > 0) {
                                        // Animate the reorder
                                        const itemElement = previewContent.querySelector(`[data-edu-id="${toolbar.dataset.eduId}"]`) as HTMLElement;
                                        if (itemElement) {
                                            itemElement.classList.add('slide-up-smooth');
                                            setTimeout(() => itemElement.classList.remove('slide-up-smooth'), 400);
                                        }

                                        // Swap items
                                        [list[currentIndex], list[currentIndex - 1]] = [list[currentIndex - 1], list[currentIndex]];
                                        m.updateSection('education', list as any);
                                        requestAnimationFrame(positionToolbar);
                                    }
                                }
                                return;
                            }

                            if (action === 'move-down') {
                                const state = m.resumeData.get();
                                if (type === 'work' && toolbar.dataset.workId) {
                                    const list = [...state.workExperience];
                                    const currentIndex = list.findIndex(item => item.id === toolbar.dataset.workId);
                                    if (currentIndex < list.length - 1) {
                                        // Animate the reorder
                                        const itemElement = previewContent.querySelector(`[data-work-id="${toolbar.dataset.workId}"]`) as HTMLElement;
                                        if (itemElement) {
                                            itemElement.classList.add('slide-down-smooth');
                                            setTimeout(() => itemElement.classList.remove('slide-down-smooth'), 400);
                                        }

                                        // Swap items
                                        [list[currentIndex], list[currentIndex + 1]] = [list[currentIndex + 1], list[currentIndex]];
                                        m.updateSection('workExperience', list as any);
                                        requestAnimationFrame(positionToolbar);
                                    }
                                } else if (type === 'edu' && toolbar.dataset.eduId) {
                                    const list = [...state.education];
                                    const currentIndex = list.findIndex(item => item.id === toolbar.dataset.eduId);
                                    if (currentIndex < list.length - 1) {
                                        // Animate the reorder
                                        const itemElement = previewContent.querySelector(`[data-edu-id="${toolbar.dataset.eduId}"]`) as HTMLElement;
                                        if (itemElement) {
                                            itemElement.classList.add('slide-down-smooth');
                                            setTimeout(() => itemElement.classList.remove('slide-down-smooth'), 400);
                                        }

                                        // Swap items
                                        [list[currentIndex], list[currentIndex + 1]] = [list[currentIndex + 1], list[currentIndex]];
                                        m.updateSection('education', list as any);
                                        requestAnimationFrame(positionToolbar);
                                    }
                                }
                                return;
                            }


                            if (action === 'ai') {
                                const detail = { type, context: { workId: toolbar.dataset.workId, eduId: toolbar.dataset.eduId, customId: toolbar.dataset.customId } };
                                document.dispatchEvent(new CustomEvent('resume-ai-request', { detail }));
                            }
                        });
                    });

                    (previewContent as any)._toolbarWired = true;

                    toolbar.addEventListener('change', (e) => {
                        const input = e.target as HTMLInputElement;
                        if (input.matches('[data-bio-field]')) {
                            const field = input.dataset.bioField;
                            if (field) {
                                const isVisible = input.checked;
                                import('../../lib/resumeBuilderService').then(m => {
                                    if (isVisible) {
                                        m.updateContactInfo(field, `[${field}]`);
                                    } else {
                                        const currentData = m.resumeData.get();
                                        delete currentData.contactInfo[field];
                                        m.setResumeData(currentData);
                                    }
                                });
                            }
                        }
                    });
                }

                // Initialize drag and drop functionality
                renderSidebarSections(data);
                setTimeout(() => {
                    addPreviewDragAndDrop();
                }, 100); // Small delay to ensure DOM is ready
            };

            // rAF batch updates
            let rafPending = false;
            let latest: any = null;
            const unsubscribe = resumeData.subscribe((data: any) => {
                console.log('ResumePreview received data update:', data);
                latest = data;

                // Keep palette UI (chips, +/- controls, visible custom list) in sync
                updatePaletteState(data);

                // If an upload was in progress and data arrived, hide the spinner
                if (isUploading) {
                  isUploading = false;
                  hideUploadOverlay();
                }

                if (rafPending || isInlineEditing) return; // don't rerender while typing
                rafPending = true;
                requestAnimationFrame(() => {
                    rafPending = false;
                    renderPreview(latest);
                });
            });

            downloadBtn.addEventListener('click', async () => {
                const preview = document.getElementById('resume-preview-content');
                if (!preview) return;

                const downloadButtonSpan = downloadBtn.querySelector('span');
                if (downloadButtonSpan) downloadButtonSpan.textContent = 'Preparing...';
                downloadBtn.disabled = true;

                try {
                    // Capture dynamic CSS so the PDF matches on-screen rendering
                    const dynamicStylesTag = document.getElementById('resume-dynamic-styles') as HTMLStyleElement | null;
                    const dynamicStyles = dynamicStylesTag?.innerHTML || '';
                    // Include current padding (set via element style in preview) so PDF spacing matches (web preview)
                    const previewEl = document.getElementById('resume-preview-content') as HTMLElement | null;
                    const padding = previewEl ? (previewEl.style.padding || getComputedStyle(previewEl).padding) : '';

                    // Force proper PDF print margins and breaking on ALL pages inside the generated PDF
                    // IMPORTANT: The server receives ONLY the innerHTML of #resume-preview-content by default.
                    // That means any CSS scoped to #resume-preview-content would NOT apply server-side.
                    // Wrap the HTML we send with the same #resume-preview-content container so styles are honored.
                    const htmlForPdf = `<div id="resume-preview-content" class="harvard">${(preview as HTMLElement).innerHTML}</div>`;

                    const pdfForceStyles = `
@page { size: A4; margin: ${(() => { const m = (resumeData.get()?.margins) || 'normal'; return m === 'narrow' ? '0.5in' : (m === 'wide' ? '1in' : '0.75in'); })()}; }
#resume-preview-content{ padding: 0 !important; margin: 0 !important; border: none !important; box-shadow: none !important; background: #fff !important; }
#resume-preview-content section,
#resume-preview-content header,
#resume-preview-content .item-container,
#resume-preview-content ul,
#resume-preview-content li { break-inside: avoid-page; page-break-inside: avoid; }
                    `.trim();

                    const mergedStyles = `${dynamicStyles}${padding ? `\n#resume-preview-content{padding:${padding}}` : ''}\n${pdfForceStyles}`;

                    const response = await fetch('/.netlify/functions/generate-pdf', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            // Send wrapped HTML so all #resume-preview-content-scoped rules apply in the renderer
                            resumeData: htmlForPdf,
                            dynamicStyles: mergedStyles,
                        }),
                    });

                    if (!response.ok) {
                        throw new Error('Failed to generate PDF');
                    }

                    const blob = await response.blob();
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = 'resume.pdf';
                    document.body.appendChild(a);
                    a.click();
                    a.remove();
                    window.URL.revokeObjectURL(url);

                } catch (error) {
                    console.error('Error downloading PDF:', error);
                    alert('Failed to download PDF. Please try again.');
                } finally {
                    if (downloadButtonSpan) downloadButtonSpan.textContent = 'Download';
                    downloadBtn.disabled = false;
                }
            });

            clearBtn.addEventListener('click', () => {
                if (confirm('Are you sure you want to clear all data and start with a blank resume?')) {
                    clearResumeData();
                }
            });

            sampleBtn.addEventListener('click', () => {
                loadSampleData();
            });

            window.addEventListener('beforeunload', unsubscribe);

            // Zoom control
            const zoom = document.getElementById('preview-zoom') as HTMLInputElement | null;
            if (zoom) {
                zoom.addEventListener('input', (e) => {
                    const v = Number((e.target as HTMLInputElement).value) / 100;
                    (previewContent as HTMLElement).style.transformOrigin = 'top center';
                    (previewContent as HTMLElement).style.transform = `scale(${v})`;
                });
            }

            // Removed the "Actual size" toggle and fit-to-view auto-scaling per request
        });
    // Global keyboard shortcuts to reorder Experience/Education entries (Alt+ArrowUp/Down)
document.addEventListener('keydown', (ev) => {
    const e = ev as KeyboardEvent;
    if (!(e.altKey && (e.key === 'ArrowUp' || e.key === 'ArrowDown'))) return;

    const toolbarEl = document.getElementById('floating-toolbar') as HTMLElement | null;
    if (!toolbarEl || toolbarEl.classList.contains('hidden')) return;

    const type = toolbarEl.dataset.targetType as 'work'|'edu'|'custom'|'contact'|undefined;
    if (type !== 'work' && type !== 'edu') return;

    const resumeContent = document.getElementById('resume-preview-content') as HTMLElement | null;
    const selected = resumeContent?.querySelector('.item-container.item-selected') as HTMLElement | null;
    const dir = e.key === 'ArrowUp' ? -1 : 1;

    import('../../lib/resumeBuilderService').then(m => {
        const state = m.resumeData.get();

        if (type === 'work') {
            const id = (selected?.dataset.workId || toolbarEl.dataset.workId) as string | undefined;
            if (!id) return;
            const list = [...state.workExperience];
            const idx = list.findIndex(it => it.id === id);
            if (idx === -1) return;
            const newIndex = idx + dir;
            if (newIndex < 0 || newIndex >= list.length) return;

            const itemElement = document.querySelector(`[data-work-id="${id}"]`) as HTMLElement | null;
            if (itemElement) {
                itemElement.classList.add(dir < 0 ? 'slide-up-smooth' : 'slide-down-smooth');
                setTimeout(() => itemElement.classList.remove(dir < 0 ? 'slide-up-smooth' : 'slide-down-smooth'), 400);
            }

            [list[idx], list[newIndex]] = [list[newIndex], list[idx]];
            m.updateSection('workExperience', list as any);
            e.preventDefault();

            // Nudge toolbar repositioning
            window.dispatchEvent(new Event('resize'));
        } else if (type === 'edu') {
            const id = (selected?.dataset.eduId || toolbarEl.dataset.eduId) as string | undefined;
            if (!id) return;
            const list = [...state.education];
            const idx = list.findIndex(it => it.id === id);
            if (idx === -1) return;
            const newIndex = idx + dir;
            if (newIndex < 0 || newIndex >= list.length) return;

            const itemElement = document.querySelector(`[data-edu-id="${id}"]`) as HTMLElement | null;
            if (itemElement) {
                itemElement.classList.add(dir < 0 ? 'slide-up-smooth' : 'slide-down-smooth');
                setTimeout(() => itemElement.classList.remove(dir < 0 ? 'slide-up-smooth' : 'slide-down-smooth'), 400);
            }

            [list[idx], list[newIndex]] = [list[newIndex], list[idx]];
            m.updateSection('education', list as any);
            e.preventDefault();

            // Nudge toolbar repositioning
            window.dispatchEvent(new Event('resize'));
        }
    });
});
</script>
