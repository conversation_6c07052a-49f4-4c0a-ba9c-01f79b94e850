---
// Form section for skills.
---

<div class="space-y-4">
  <div>
    <label for="skills" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Skills</label>
    <textarea id="skills" name="skills" rows="6" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-lg shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-800 dark:text-white"></textarea>
    <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">
      Enter skills in categorized format (e.g., Technical Skills: JavaScript, React, Node.js) or as a simple list.
    </p>
  </div>
</div>

<script>
  import { resumeData, updateSection } from '../../../lib/resumeBuilderService';

  document.addEventListener('DOMContentLoaded', () => {
    const skillsTextarea = document.getElementById('skills') as HTMLTextAreaElement;

    if (!skillsTextarea) return;

    // Initial population from store
    const currentData = resumeData.get();
    if (currentData.skills && currentData.skills.length > 0) {
      // Check if skills is an array of categorized skills or a flat array
      if (typeof currentData.skills[0] === 'object' && currentData.skills[0].category) {
        // Categorized skills format
        skillsTextarea.value = currentData.skills.map(cat => `${cat.category}: ${cat.items.join(', ')}`).join('\n');
      } else {
        // Flat array format
        skillsTextarea.value = (currentData.skills as string[]).join('\n');
      }
    }

    const unsubscribe = resumeData.subscribe(data => {
      if (data.skills) {
        let skillsString = '';
        // Check if skills is an array of categorized skills or a flat array
        if (typeof data.skills[0] === 'object' && data.skills[0].category) {
          // Categorized skills format
          skillsString = data.skills.map(cat => `${cat.category}: ${cat.items.join(', ')}`).join('\n');
        } else {
          // Flat array format
          skillsString = (data.skills as string[]).join('\n');
        }
        if (skillsTextarea.value !== skillsString) {
          skillsTextarea.value = skillsString;
        }
      }
    });

    const debounce = (fn: (...args: any[]) => void, ms = 200) => {
      let t: number | null = null;
      return (...args: any[]) => {
        if (t) window.clearTimeout(t);
        t = window.setTimeout(() => fn(...args), ms);
      };
    };

    const debounced = debounce((value: string) => {
      // Check if the input contains categorized skills (lines with colons)
      const lines = value.split('\n').map(line => line.trim()).filter(line => line);
      
      if (lines.some(line => line.includes(':'))) {
        // Parse categorized skills
        const categorizedSkills = lines.map(line => {
          const [category, itemsStr] = line.split(':').map(part => part.trim());
          const items = itemsStr ? itemsStr.split(',').map(item => item.trim()).filter(item => item) : [];
          return { category, items };
        }).filter(cat => cat.category && cat.items.length > 0);
        
        updateSection('skills', categorizedSkills);
      } else {
        // Parse as flat array (split on both commas and new lines)
        const skillsArray = value.split(/[\n,]+/).map(skill => skill.trim()).filter(skill => skill);
        updateSection('skills', skillsArray);
      }
    });

    skillsTextarea.addEventListener('input', (e) => {
      const target = e.target as HTMLTextAreaElement;
      debounced(target.value);
    });

    window.addEventListener('beforeunload', unsubscribe);
  });
</script>