---
 // src/components/resume-builder/FloatingToolbar.astro
---
<div id="floating-toolbar" class="absolute z-50 hidden bg-white/95 dark:bg-gray-800/95 rounded-lg shadow-lg ring-1 ring-gray-200 dark:ring-gray-700 px-2 py-1.5">
  <div class="flex items-center gap-2">
    <!-- Bullet Actions Dropdown - Hidden for summary and header sections -->
    <div class="relative bullet-dropdown" data-dropdown="bullet">
      <button class="toolbar-btn flex items-center" title="Bullet options" data-dropdown-toggle="bullet">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2" aria-hidden="true">
          <path stroke-linecap="round" stroke-linejoin="round" d="M12 4v16m8-8H4" />
        </svg>
        <span class="text-xs font-medium">Bullet</span>
        <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" d="M19 9l-7 7-7-7" />
        </svg>
      </button>
      <div class="absolute left-0 mt-1 w-32 rounded-md shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 hidden z-50" data-dropdown-menu="bullet">
        <div class="py-1" role="menu" aria-orientation="vertical" aria-labelledby="options-menu">
          <button data-action="add-bullet" class="block w-full text-left px-4 py-2 text-xs hover:bg-gray-100 dark:hover:bg-gray-700" role="menuitem">
            Add Bullet
          </button>
          <button data-action="delete-bullet" class="block w-full text-left px-4 py-2 text-xs hover:bg-gray-100 dark:hover:bg-gray-700" role="menuitem">
            Delete Bullet
          </button>
        </div>
      </div>
    </div>
 
    <!-- Fields Dropdown - Visible only for header/bio -->
    <div class="relative fields-dropdown hidden" data-dropdown="fields">
        <button class="toolbar-btn flex items-center" title="Bio fields" data-dropdown-toggle="fields">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2" aria-hidden="true">
            <path stroke-linecap="round" stroke-linejoin="round" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
          </svg>
          <span class="text-xs font-medium">Bio Fields</span>
            <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="M19 9l-7 7-7-7" />
            </svg>
        </button>
        <div class="absolute left-0 mt-1 w-48 rounded-md shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 hidden z-50" data-dropdown-menu="fields">
            <div class="py-1" role="menu" aria-orientation="vertical" aria-labelledby="fields-menu">
                <div id="bio-fields-list" class="px-4 py-2 text-xs">
                    <!-- Populated by JS -->
                </div>
                <div class="border-t border-gray-200 dark:border-gray-700 my-1"></div>
                <button data-action="add-bio-field" class="block w-full text-left px-4 py-2 text-xs hover:bg-gray-100 dark:hover:bg-gray-700" role="menuitem">
                    Add Field
                </button>
            </div>
        </div>
    </div>
 
    <!-- Move Actions Dropdown - Hidden for summary and header sections -->
    <div class="relative move-dropdown" data-dropdown="move">
      <button class="toolbar-btn flex items-center" title="Move options" data-dropdown-toggle="move">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2" aria-hidden="true">
          <path stroke-linecap="round" stroke-linejoin="round" d="M7 16V8m0 0V4a1 1 0 011-1h11a1 1 0 011 1v3M7 16v4a1 1 0 001 1h11a1 1 0 001-1v-4M7 16L3 12m4 4l4-4" />
        </svg>
        <span class="text-xs font-medium">Move</span>
        <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" d="M19 9l-7 7-7-7" />
        </svg>
      </button>
      <div class="absolute left-0 mt-1 w-32 rounded-md shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 hidden z-50" data-dropdown-menu="move">
        <div class="py-1" role="menu" aria-orientation="vertical" aria-labelledby="options-menu">
          <button data-action="move-up" class="block w-full text-left px-4 py-2 text-xs hover:bg-gray-100 dark:hover:bg-gray-700" role="menuitem">
            Move Up
          </button>
          <button data-action="move-down" class="block w-full text-left px-4 py-2 text-xs hover:bg-gray-100 dark:hover:bg-gray-700" role="menuitem">
            Move Down
          </button>
        </div>
      </div>
    </div>

    <!-- Separator -->
    <div class="w-px h-6 bg-gray-300 dark:bg-gray-600"></div>


    <!-- AI Assistant -->
    <button data-action="ai" class="toolbar-btn" title="AI Assistant">
      <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2" aria-hidden="true">
        <path stroke-linecap="round" stroke-linejoin="round" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
      </svg>
      <span class="text-xs font-medium">AI</span>
    </button>

    <!-- Delete -->
    <button data-action="delete" class="toolbar-btn text-rose-600 hover:text-rose-700" title="Delete">
      <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2" aria-hidden="true">
        <path stroke-linecap="round" stroke-linejoin="round" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6M9 7V4a1 1 0 011-1h4a1 1 0 011 1v3M4 7h16" />
      </svg>
      <span class="text-xs font-medium">Delete</span>
    </button>
  </div>
</div>

<style>
  .toolbar-btn {
    @apply px-2 py-1.5 rounded-md text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 transition inline-flex items-center justify-center min-w-[32px];
  }
  .btn-primary {
    @apply bg-emerald-500 text-white hover:bg-emerald-600 dark:bg-emerald-600 dark:hover:bg-emerald-700 rounded-md px-3 py-1.5 font-medium;
  }
  #floating-toolbar {
    backdrop-filter: blur(6px);
  }
</style>


<script>
  // Add JavaScript to handle dropdown functionality
  document.addEventListener('DOMContentLoaded', () => {
    // Get all dropdown toggle buttons
    const dropdownToggles = document.querySelectorAll('[data-dropdown-toggle]');
    
    dropdownToggles.forEach(toggle => {
      toggle.addEventListener('click', (e: Event) => {
        e.stopPropagation();
        const dropdownId = toggle.getAttribute('data-dropdown-toggle');
        const dropdownMenu = document.querySelector(`[data-dropdown-menu="${dropdownId}"]`);
        
        // Close all other dropdowns
        document.querySelectorAll('[data-dropdown-menu]').forEach(menu => {
          if (menu !== dropdownMenu) {
            menu.classList.add('hidden');
          }
        });
        
        // Toggle the current dropdown
        if (dropdownMenu) {
          dropdownMenu.classList.toggle('hidden');
        }
      });
    });
    
    // Close dropdowns when clicking outside
    document.addEventListener('click', (e: Event) => {
      const target = e.target as HTMLElement | null;
      if (target && !target.closest('[data-dropdown]')) {
        document.querySelectorAll('[data-dropdown-menu]').forEach(menu => {
          menu.classList.add('hidden');
        });
      }
    });
    
    // Close dropdowns when clicking on a menu item
    document.querySelectorAll('[data-dropdown-menu] button').forEach(button => {
      button.addEventListener('click', () => {
        document.querySelectorAll('[data-dropdown-menu]').forEach(menu => {
          menu.classList.add('hidden');
        });
      });
    });
  });
</script>