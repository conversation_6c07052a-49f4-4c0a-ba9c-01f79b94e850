import { Handler } from '@netlify/functions';
import puppeteer from 'puppeteer';
import fs from 'fs/promises';
import path from 'path';

const sanitizeHTML = (html: string) =>
  (html || '')
    .replace(/\scontenteditable="true"/gi, '')
    .replace(/\scontenteditable=true/gi, '')
    .replace(/\sdata-placeholder="[^"]*"/gi, '');

const handler: Handler = async (event) => {
  if (event.httpMethod !== 'POST') {
    return { statusCode: 405, body: 'Method Not Allowed' };
  }

  const { resumeData, dynamicStyles } = JSON.parse(event.body || '{}');

  if (!resumeData) {
    return { statusCode: 400, body: 'Bad Request: Missing resume data.' };
  }

  try {
    const [template, css] = await Promise.all([
      fs.readFile(path.resolve(process.cwd(), 'src/templates/resume-template.html'), 'utf-8'),
      fs.readFile(path.resolve(process.cwd(), 'src/styles/resume-template.css'), 'utf-8'),
    ]);

    // Inline CSS so <PERSON><PERSON>peteer doesn't have to resolve external links,
    // and add dynamic styles coming from the live preview.
    const html = template
      .replace(
        '<link rel="stylesheet" href="resume-template.css">',
        `<style>
${css}

/* Dynamic styles from client preview */
${dynamicStyles || ''}

/* Force doc-level page size and margins so every page matches the preview */
@page { size: A4; }

/* Defensive reset to rely on page margins in PDFs */
#resume-preview-content { padding: 0 !important; margin: 0 !important; border: none !important; box-shadow: none !important; background: #fff !important; }

/* Keep blocks intact across pages */
#resume-preview-content section,
#resume-preview-content header,
#resume-preview-content .item-container,
#resume-preview-content ul,
#resume-preview-content li { break-inside: avoid-page; page-break-inside: avoid; }

/* Hide interactive controls defensively */
[data-action], .cursor-grab, .inline-popover, #inline-toolbar { display: none !important; }
</style>`
      )
      .replace('<!-- Resume content will be injected here -->', sanitizeHTML(resumeData));

    const browser = await puppeteer.launch({
      args: ['--no-sandbox', '--font-render-hinting=medium'],
    });
    const page = await browser.newPage();

    await page.setContent(html, { waitUntil: 'networkidle0' });

    // As an additional safeguard, normalize DOM and strip interactive attributes/nodes.
    await page.evaluate(() => {
      // If the client sent an extra #resume-preview-content wrapper, flatten it
      const host = document.getElementById('resume-preview-content');
      const first = host && host.firstElementChild;
      if (host && first && (first as HTMLElement).id === 'resume-preview-content') {
        host.replaceWith(first);
      }

      // Hide/remove interactive nodes if any remain
      document
        .querySelectorAll('[data-action], .cursor-grab, .inline-popover, #inline-toolbar')
        .forEach((el) => ((el as HTMLElement).style.display = 'none'));

      // Also strip contenteditable attributes if present.
      document.querySelectorAll('[contenteditable]').forEach((el) => el.removeAttribute('contenteditable'));
    });

    // Ensure print-specific CSS is honored
    await page.emulateMediaType('print');

    const pdfBuffer = await page.pdf({
      format: 'A4',
      printBackground: true,
      preferCSSPageSize: true,
      margin: { top: '0', right: '0', bottom: '0', left: '0' },
    });

    await browser.close();

    return {
      statusCode: 200,
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': 'attachment; filename="resume.pdf"',
      },
      body: Buffer.from(pdfBuffer).toString('base64'),
      isBase64Encoded: true,
    };
  } catch (error) {
    console.error('Error generating PDF:', error);
    return { statusCode: 500, body: 'Internal Server Error' };
  }
};

export { handler };